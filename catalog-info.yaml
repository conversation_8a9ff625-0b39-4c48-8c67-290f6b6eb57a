apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: patient-webapp
  description: FluentHealth companion web app
  tags:
    - reactjs
    - typescript

  links:
    - url: https://console.firebase.google.com/u/0/project/fh-dev-svc/hosting/sites/fh-dev-svc-webapp
      title: Deployment Link (Dev)
      icon: externalLink
    - url: https://fh-dev-svc-webapp.web.app/
      title: Link (Dev)
      icon: externalLink

spec:
  type: website
  lifecycle: production
  owner: group:webapp

  consumesApis:
    - apim-emr
    - apim-cms
    - apim-content-library
    - apim-auth-service
