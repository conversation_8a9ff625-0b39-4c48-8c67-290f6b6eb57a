{"name": "@fluent-health/webapp", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "npm run codegen && tsc && vite build", "build:fh-dev-svc": "npm run codegen && tsc && vite build --mode dev", "build:fh-test-svc": "npm run codegen && tsc && vite build --mode staging", "build:fh-prod-svc": "npm run codegen && tsc && vite build --mode production", "build:dev": "npm run build -- --mode dev", "build:staging": "npm run build -- --mode staging", "preview": "vite preview", "pretty-quick": "pretty-quick --pattern \"./**/*\"", "format:all": "prettier --write .", "lint": "eslint src/**/*.{ts,tsx}", "lint:fix": "eslint --fix .", "codegen": "graphql-codegen --config codegen.ts"}, "dependencies": {"@chakra-ui/anatomy": "2.2.0", "@chakra-ui/icons": "2.0.17", "@chakra-ui/react": "2.5.5", "@formkit/auto-animate": "1.0.0-beta.6", "@hookform/resolvers": "2.9.11", "@microsoft/clarity": "^1.0.0", "@radix-ui/react-dropdown-menu": "2.0.2", "@tanstack/react-query": "4.22.0", "@tanstack/react-query-devtools": "4.22.0", "@tanstack/react-table": "8.7.6", "axios": "^1.6.2", "branch-sdk": "^2.86.3", "buffer": "^6.0.3", "calendar-link": "2.5.1", "class-variance-authority": "^0.7.0", "clevertap-web-sdk": "^1.15.3", "clsx": "1.2.1", "core-js": "3.32.0", "crypto-hash": "^3.1.0", "d3": "^7.9.0", "date-fns": "^4.1.0", "dayjs": "1.11.7", "framer-motion": "10.0.1", "graphql": "^16.8.1", "html-react-parser": "^5.1.18", "html2canvas": "^1.4.1", "jwt-decode": "3.1.2", "localforage": "1.10.0", "lodash": "^4.17.21", "mixpanel-browser": "^2.64.0", "pdfjs-dist": "^2.16.105", "react": "18.2.0", "react-datepicker": "4.11.0", "react-dom": "18.2.0", "react-dropzone": "14.2.3", "react-feather": "2.0.10", "react-hook-form": "7.43.1", "react-pdf": "6.2.2", "react-router-dom": "6.6.1", "react-select": "5.7.0", "recharts": "^2.6.2", "swiper": "9.2.0", "universal-cookie": "4.0.4", "urql": "^4.0.7", "use-resize-observer": "9.1.0", "usehooks-ts": "2.9.1", "uuid": "^10.0.0", "vite-plugin-svgr": "2.4.0", "vite-tsconfig-paths": "4.0.5", "zod": "^3.22.4", "zustand": "4.2.0"}, "devDependencies": {"@graphql-codegen/cli": "5.0.2", "@graphql-codegen/client-preset": "4.2.5", "@graphql-codegen/introspection": "4.0.3", "@parcel/watcher": "^2.4.1", "@types/branch-sdk": "^2.53.7", "@types/clevertap-web-sdk": "^1.1.12", "@types/d3": "^7.4.3", "@types/mixpanel-browser": "^2.60.0", "@types/react": "18.0.26", "@types/react-datepicker": "4.10.0", "@types/react-dom": "18.0.10", "@types/react-pdf": "6.2.0", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "5.59.1", "@typescript-eslint/parser": "5.59.1", "@vitejs/plugin-react-swc": "3.0.1", "autoprefixer": "10.4.13", "eslint": "8.39.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-typescript": "^17.0.0", "eslint-config-prettier": "8.8.0", "eslint-config-standard-with-typescript": "34.0.1", "eslint-plugin-import": "2.27.5", "eslint-plugin-jsx-a11y": "6.7.1", "eslint-plugin-n": "15.7.0", "eslint-plugin-prettier": "4.2.1", "eslint-plugin-promise": "6.1.1", "eslint-plugin-react": "7.32.2", "postcss": "^8.4.32", "postcss-import": "15.1.0", "prettier": "2.8.4", "prettier-plugin-tailwindcss": "0.2.2", "pretty-quick": "3.1.3", "tailwindcss": "3.2.4", "typescript": "^5.4.4", "vite": "^4.5.1", "vite-plugin-html": "3.2.0"}}