/**
 * Generate an array of height options in the format of feet and inches.
 * @returns {string[]} An array of height options from 3'0" to 9'11".
 */
export const heightOptions = Array.from({ length: 85 }, (_, i) => `${3 + Math.floor(i / 12)}'${i % 12}"`);

/**
 * Convert a height value from feet and inches to centimeters.
 * @param {string} value - The height value in the format of feet and inches (e.g., "5'10").
 * @returns {number} The height value in centimeters.
 */
export const convertFeetAndInchesToCm = (value: string): number => {
  const [feet, inches] = value.split("'").map((v) => parseInt(v, 10));
  const totalInches = feet * 12 + inches;
  const cm = parseFloat((totalInches * 2.54).toFixed(2));
  return cm;
};

/**
 * Generate an array of height options in centimeters, matching the inch options.
 * @returns {string[]} An array of height options corresponding to the feet/inches options, rounded to nearest cm.
 */
export const cmOptions = heightOptions.map((h) => `${Math.round(convertFeetAndInchesToCm(h))} cm`);

/**
 * Convert a height value from centimeters to feet and inches.
 * @param {number} value - The height value in centimeters.
 * @returns {string} The height value in the format of feet and inches (e.g., "5'10").
 */
export const convertCmToFeetAndInches = (value: number): string => {
  const inches = value / 2.54;
  const feet = Math.floor(inches / 12);
  const remainingInches = Math.round(inches % 12);
  return `${feet}'${remainingInches}"`;
};

/**
 * Converts weight from kilograms to pounds and formats it.
 *
 * @param {string} weightInKg - The weight in kilograms as a string.
 * @returns {string} - The formatted weight in pounds.
 */
export const convertWeightKgToLbs = (weightInKg: string): string => {
  const weightInKgFloat = parseFloat(weightInKg);
  const weightInLbs = weightInKgFloat * 2.20462;
  return `${weightInLbs.toFixed(weightInLbs % 1 === 0 ? 0 : 2)}`; // lbs
};

/**
 * Converts weight from pounds to kilograms and formats it.
 *
 * @param {string} weightInKg - The weight in pounds as a string.
 * @returns {string} - The formatted weight in kilograms.
 */
export const convertWeightLbsToKg = (weightInLbs: string): string => {
  const weightInKg = parseFloat(weightInLbs) / 2.20462;
  // const weightInKg = (weightInLbsConvert);
  return weightInKg.toFixed(weightInKg % 1 === 0 ? 0 : 2); // kg
};
