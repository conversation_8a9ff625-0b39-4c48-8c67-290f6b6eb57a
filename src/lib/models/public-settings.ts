import { Allergy } from '@lib/models/allergies';
import { Condition } from '@lib/models/condition';
import { EmergencyContact } from '@lib/models/emergency-contact';
import { HealthcareProxy } from '@lib/models/healthcare-proxy';
import { HealthInsurance } from '@lib/models/health-insurance';
import { Immunization } from '@lib/models/immunization';
import { MedicalRecord } from '@lib/models/medical-record';
import { Medication } from '@lib/models/medication';
import { Patient } from '@lib/models/patient';
import { PatientVitals } from '@lib/models/patient-vitals';
import { RelatedPerson } from '@lib/models/related-person';
import { Screening } from '@lib/models/screening';
import { PastSurgery } from '@lib/models/surgery-proceudure';
import { Symptom } from '@lib/models/symptoms';
import { FamilyMemberHistory } from './family-member-history';

export enum PUBLIC_SETTINGS_PROPERTY_NAME {
  EXPIRES_IN = 'expiry_date',
  SHOW_LAST_NAME = 'lastName',
  EMERGENCY_CONTACT_SECTION = 'emergencyContacts',
  HEALTHCARE_PROXY_SECTION = 'alternativeMedicalDecisionMaker',
  HEALTH_INSURANCE_SECTION = 'healthInsurance',
  VITALS = 'vitals',
  SHOW_CONDITIONS = 'show_conditions',
  MY_CONDITIONS = 'myConditions',
  MY_FAMILY_HISTORY = 'myFamilyHistory',
  MY_SYMPTOMS = 'mySymptoms',
  MY_MEDICATIONS = 'myMedications',
  MY_ALLERGIES_INTOLERANCES = 'myAllergiesIntolerances',
  MY_SURGERIES_PROCEDURES = 'mySurgeriesProcedures',
  MY_IMMUNIZATIONS = 'myImmunizations',
  MY_PREVENTATIVE_SCREENINGS = 'myPreventativeScreenings',
  MY_REPRODUCTIVE_HEALTH = 'myReproductiveHealth',
  MY_LIFESTYLES = 'myLifestyles',
  EXTERNAL_REPORTS = 'external-reports',
  CARE_TEAM_SECTION = 'careTeam',
  FAMILY_MEMBER_HISTORY_SECTION = 'familyMemberHistory',
  MY_CONSENTS = 'myConsent',
  MY_CONSENT_FEEDBACK = 'myConsentFeedback',
}

export type PublicSettingsResponse = {
  sToken: string;
};

type CommonPublicConditions = {
  [PUBLIC_SETTINGS_PROPERTY_NAME.MY_CONDITIONS]: Condition[];
  [PUBLIC_SETTINGS_PROPERTY_NAME.MY_FAMILY_HISTORY]: RelatedPerson[];
  [PUBLIC_SETTINGS_PROPERTY_NAME.MY_SYMPTOMS]: Symptom[];
  [PUBLIC_SETTINGS_PROPERTY_NAME.MY_MEDICATIONS]: Medication[];
  [PUBLIC_SETTINGS_PROPERTY_NAME.MY_ALLERGIES_INTOLERANCES]: Allergy[];
  [PUBLIC_SETTINGS_PROPERTY_NAME.MY_SURGERIES_PROCEDURES]: PastSurgery[];
  [PUBLIC_SETTINGS_PROPERTY_NAME.MY_PREVENTATIVE_SCREENINGS]: Screening[];
  [PUBLIC_SETTINGS_PROPERTY_NAME.MY_IMMUNIZATIONS]: Immunization[];
  [PUBLIC_SETTINGS_PROPERTY_NAME.MY_LIFESTYLES]: [];
  [PUBLIC_SETTINGS_PROPERTY_NAME.MY_REPRODUCTIVE_HEALTH]: [];
};

export type CommonPublicShareablePayloadData = {
  id: string;
  is_shareable: boolean;
};

export type PublicSettingsPayload = {
  [PUBLIC_SETTINGS_PROPERTY_NAME.EXPIRES_IN]: string;
  [PUBLIC_SETTINGS_PROPERTY_NAME.SHOW_LAST_NAME]: boolean;
  [PUBLIC_SETTINGS_PROPERTY_NAME.EMERGENCY_CONTACT_SECTION]: boolean;
  [PUBLIC_SETTINGS_PROPERTY_NAME.HEALTHCARE_PROXY_SECTION]: boolean;
  [PUBLIC_SETTINGS_PROPERTY_NAME.HEALTH_INSURANCE_SECTION]: boolean;
  [PUBLIC_SETTINGS_PROPERTY_NAME.VITALS]: boolean;
  [PUBLIC_SETTINGS_PROPERTY_NAME.SHOW_CONDITIONS]: boolean;
  conditions: {
    [PUBLIC_SETTINGS_PROPERTY_NAME.MY_CONDITIONS]: CommonPublicShareablePayloadData[];
    [PUBLIC_SETTINGS_PROPERTY_NAME.MY_FAMILY_HISTORY]: Array<
      CommonPublicShareablePayloadData & {
        conditions: CommonPublicShareablePayloadData[];
        procedures: CommonPublicShareablePayloadData[];
      }
    >;
    [PUBLIC_SETTINGS_PROPERTY_NAME.MY_SYMPTOMS]: CommonPublicShareablePayloadData[];
    [PUBLIC_SETTINGS_PROPERTY_NAME.MY_MEDICATIONS]: CommonPublicShareablePayloadData[];
    [PUBLIC_SETTINGS_PROPERTY_NAME.MY_ALLERGIES_INTOLERANCES]: CommonPublicShareablePayloadData[];
    [PUBLIC_SETTINGS_PROPERTY_NAME.MY_SURGERIES_PROCEDURES]: CommonPublicShareablePayloadData[];
    [PUBLIC_SETTINGS_PROPERTY_NAME.MY_PREVENTATIVE_SCREENINGS]: CommonPublicShareablePayloadData[];
    [PUBLIC_SETTINGS_PROPERTY_NAME.MY_IMMUNIZATIONS]: CommonPublicShareablePayloadData[];
  };
  [PUBLIC_SETTINGS_PROPERTY_NAME.MY_CONSENTS]: boolean;
};

export type PublicProfileDetails = {
  patient: Patient;
  [PUBLIC_SETTINGS_PROPERTY_NAME.EMERGENCY_CONTACT_SECTION]: EmergencyContact[];
  [PUBLIC_SETTINGS_PROPERTY_NAME.HEALTH_INSURANCE_SECTION]: HealthInsurance[];
  [PUBLIC_SETTINGS_PROPERTY_NAME.HEALTHCARE_PROXY_SECTION]: HealthcareProxy[];
  [PUBLIC_SETTINGS_PROPERTY_NAME.CARE_TEAM_SECTION]: any[];
  [PUBLIC_SETTINGS_PROPERTY_NAME.VITALS]: PatientVitals[];
  conditions: CommonPublicConditions;
  [PUBLIC_SETTINGS_PROPERTY_NAME.FAMILY_MEMBER_HISTORY_SECTION]: FamilyMemberHistory[];
};

// Medical Records
export type PublicMedicalRecordSettingsPayload = {
  [PUBLIC_SETTINGS_PROPERTY_NAME.EXPIRES_IN]: string;
  [PUBLIC_SETTINGS_PROPERTY_NAME.EXTERNAL_REPORTS]: CommonPublicShareablePayloadData[];
};

export type PublicMedicalRecordDetails = Array<MedicalRecord>;
