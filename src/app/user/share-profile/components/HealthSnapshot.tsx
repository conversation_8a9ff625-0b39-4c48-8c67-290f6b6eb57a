import React, { useMemo, useState } from 'react';
import { Box, Button, Divider, Flex, Grid, HStack, Text, VStack } from '@chakra-ui/react';
import { useGetShareSnapshotData } from '@user/lib/medplum-state';
import dayjs from 'dayjs';
import { useSharePatient } from '@user/lib/state';

import EmptyState from './EmptyState';
import { truncateLength } from '@lib/utils/utils';
import { FH_STRUCTURE_DEFINITION_CLINICALSTATUS } from 'src/constants/medplumConstants';

import { ReactComponent as SymptomsIcon } from '@assets/icons/symptoms-icon.svg';
import { ReactComponent as ConditionIcon } from '@assets/icons/condition-icon.svg';
import { ReactComponent as MedicationSupplementsIcon } from '@assets/icons/medication-supplements-icon.svg';
import { ReactComponent as AllergiesIcon } from '@assets/icons/allergies-icon.svg';
import { ReactComponent as ReproductiveHealthIcon } from '@assets/icons/reproductive-health-icon.svg';
import { ReactComponent as FamilyHistoryIcon } from '@assets/icons/family-history-icon.svg';
import { ReactComponent as LifestyleBasicsIcon } from '@assets/icons/lifestyle-basics-icon.svg';
import { ReactComponent as DietIcon } from '@assets/icons/diet.svg';
import { ReactComponent as ExerciseIcon } from '@assets/icons/exercise.svg';
import { ReactComponent as SleepIcon } from '@assets/icons/sleep.svg';
import { ReactComponent as StressIcon } from '@assets/icons/stress.svg';
import { ReactComponent as DrinkingHabitsIcon } from '@assets/icons/drinking-habits.svg';
import { ReactComponent as SmokingHabitsIcon } from '@assets/icons/smoking-habits.svg';

function formatReproductiveHealth(reproductiveHealthData: any) {
  const items = reproductiveHealthData[0]?.item || [];

  const statusItem = items.find((it: any) => it.linkId === 'reproductive-health-current-pregnancy-status');
  const lmpItem = items.find((it: any) => it.linkId === 'reproductive-health-last-menstrual-period-date');

  const pregnancyStatus = statusItem?.answer?.[0]?.valueCoding?.display ?? 'N/A';

  const lastMenstrualPeriod =
    lmpItem?.answer?.[0]?.valueCoding?.display ||
    (lmpItem?.answer?.[0]?.valueDate ? dayjs(lmpItem.answer[0].valueDate).format('DD-MMM-YYYY') : 'N/A');

  return [
    { name: pregnancyStatus, startData: 'Pregnancy status' },
    { name: lastMenstrualPeriod, startData: 'Last menstrual period date' },
  ];
}
const getClinicalStatus = (c: any) =>
  c.extension?.find((ext: any) => ext.url === FH_STRUCTURE_DEFINITION_CLINICALSTATUS)?.valueCodeableConcept?.coding?.[0]
    ?.code;

const getTruncatedName = (name: string, severity: any, status: any) => {
  const normalizedSeverity = severity === "I don't know" ? null : severity;
  const hasBothSeverityAndStatus = normalizedSeverity && status;
  const hasEitherSeverityOrStatus = normalizedSeverity || status;

  if (hasBothSeverityAndStatus) {
    return name.length > 14 ? `${truncateLength(name, 14)}...` : name;
  }
  if (hasEitherSeverityOrStatus) {
    return name.length > 24 ? `${truncateLength(name, 24)}...` : name;
  }
  return name;
};
function formatSymptoms(SymptomsData: any) {
  return SymptomsData.map((symptom: any) => {
    const name = symptom?.code?.coding?.[0]?.display ?? 'Unknown';
    const start = symptom?.effectivePeriod?.start ? dayjs(symptom?.effectivePeriod.start).format('DD-MMM-YYYY') : null;

    const end =
      getClinicalStatus(symptom) === 'inactive'
        ? symptom?.effectivePeriod?.end
          ? dayjs(symptom?.effectivePeriod?.end).format('DD MMM YYYY')
          : 'Unknown'
        : 'Present';
    const severity = symptom?.valueString;
    const status = (() => {
      if (!symptom?.effectivePeriod?.start) return 'Inactive';
      if (end === 'Present') return 'Active';
      if (end === 'Unknown' || !symptom?.effectivePeriod?.end) return 'Inactive';
      if (symptom?.effectivePeriod?.end) {
        return dayjs(symptom?.effectivePeriod.end).isAfter(dayjs()) ||
          dayjs(symptom?.effectivePeriod.end).isSame(dayjs(), 'day')
          ? 'Active'
          : 'Inactive';
      }
      return 'Inactive';
    })();
    return {
      name,
      startData: start,
      endData: end,
      severity,
      status,
    };
  });
}
function formatMedications(medicationsSupplementsData: any) {
  return medicationsSupplementsData.map((item: any) => {
    const name = item.medicationCodeableConcept?.coding?.[0]?.display ?? 'Unknown';
    const start = item.effectivePeriod?.start ? dayjs(item.effectivePeriod.start).format('DD-MMM-YYYY') : null;
    const end = item.effectivePeriod?.end ? dayjs(item.effectivePeriod.end).format('DD-MMM-YYYY') : 'Present';
    return {
      name,
      startData: start,
      endData: end,
    };
  });
}

function formatAllergies(allergiesData: any) {
  return allergiesData
    .filter((allergy: any) => allergy?.clinicalStatus?.coding?.[0]?.code === 'active')
    .map((allergy: any) => {
      const name = allergy.code?.coding?.[0]?.display ?? 'Unknown';
      const startData = allergy.category
        ? allergy.category.map((c: any) => c.charAt(0).toUpperCase() + c.slice(1)).join(', ')
        : null;
      let severity = 'Don’t know';
      if (allergy.criticality === 'high') severity = 'High';
      else if (allergy.criticality === 'low') severity = 'Low';
      else if (allergy.criticality === 'unable-to-assess') severity = 'Don’t know';
      return {
        name,
        startData,
        severity,
      };
    });
}

function formatConditions(conditionsData: any) {
  return conditionsData.map((condition: any) => {
    const name = condition?.code?.coding?.[0]?.display ?? 'Unknown';
    const start = condition?.onsetPeriod?.start ? condition?.onsetPeriod.start : null;
    const end =
      condition?.clinicalStatus?.coding?.[0]?.code === 'inactive'
        ? condition?.onsetPeriod?.end
          ? dayjs(condition?.onsetPeriod?.end).format('DD MMM YYYY')
          : 'Unknown'
        : 'Present';
    const severity = condition?.extension?.[0]?.valueCodeableConcept?.coding?.[0]?.display ?? null;
    const status = (() => {
      if (!condition?.effectivePeriod?.start) return 'Inactive';
      if (end === 'Present') return 'Active';
      if (end === 'Unknown' || !condition?.onsetPeriod?.end) return 'Inactive';
      return dayjs(condition?.onsetPeriod.end).isAfter(dayjs()) ||
        dayjs(condition?.onsetPeriod.end).isSame(dayjs(), 'day')
        ? 'Active'
        : 'Inactive';
    })();
    return {
      name,
      startData: start,
      endData: end,
      severity,
      status,
    };
  });
}

// Utility function to extract Family Member History data
const extractFamilyMemberHistoryData = (historyList: any) => {
  const conditionMap = new Map();
  historyList.forEach((entry: any) => {
    const relationship = entry.relationship?.coding?.[0]?.display;
    if (!relationship || !entry.condition) return;

    entry.condition?.forEach((condition: any) => {
      const conditionDisplay = condition?.code?.coding?.[0]?.display ?? 'Unknown Condition';

      if (!conditionMap.has(conditionDisplay)) {
        conditionMap.set(conditionDisplay, new Set());
      }

      conditionMap.get(conditionDisplay).add(relationship);
    });
  });

  // Convert to desired format
  const result = Array.from(conditionMap.entries()).map(([title, relationships]) => ({
    name: title,
    startData: Array.from(relationships).join(', '),
  }));

  return result;
};

// Utility function to extract lifestyle data
const extractLifestyleData = (answerLists: {
  answerListExercise: any[];
  answerListDiet: any[];
  answerListMentalHealthSleep: any[];
  answerListAlcoholTobaccoCaffeine: any[];
}) => {
  const { answerListExercise, answerListDiet, answerListMentalHealthSleep, answerListAlcoholTobaccoCaffeine } =
    answerLists;

  // Helper function to extract answer value
  const extractAnswer = (answerList: any[], linkId: string) => {
    if (!Array.isArray(answerList) || answerList.length === 0) {
      return null;
    }
    return answerList[0]?.item?.find((item: any) => item.linkId === linkId)?.answer?.[0]?.valueCoding?.display;
  };

  return {
    exerciseTimesPerWeek: extractAnswer(answerListExercise, 'lifestyle-nutrition-exercise-in-week'),
    dietDescriptions: extractAnswer(answerListDiet, 'lifestyle-nutrition-diet-you-describe-your-diet'),
    sleepHours: extractAnswer(
      answerListMentalHealthSleep,
      'lifestyle-nutrition-mental-health-sleep-hours-do-you-sleep'
    ),
    stressLevel: extractAnswer(
      answerListMentalHealthSleep,
      'lifestyle-nutrition-mental-health-sleep-average-level-stress'
    ),
    drinkingHabits: extractAnswer(
      answerListAlcoholTobaccoCaffeine,
      'lifestyle-nutrition-alcohol-tobacco-caffeine-alcoholic-drinks-per-week'
    ),
    smokingHabits: extractAnswer(
      answerListAlcoholTobaccoCaffeine,
      'lifestyle-nutrition-alcohol-tobacco-caffeine-smoke-tobacco'
    ),
  };
};

interface HealthData {
  id?: string;
  icon?: React.ReactNode;
  name: string;
  value?: string;
  startData?: string;
  endData?: string;
  severity?: 'Mild' | 'Moderate' | 'Severe' | 'High' | 'Low' | 'Don’t know' | 'Acute' | 'Chronic' | "I don't know";
  status?: 'Inactive' | 'Active';
}

interface HealthSection {
  id: string;
  title: string;
  icon: React.ReactNode;
  data: HealthData[];
}

// Local severity color mapping (overrides imported one for this component)
const LOCAL_SEVERITY_COLORS = {
  Mild: { bg: 'iris.100', color: 'iris.600' },
  Moderate: { bg: 'beige.200', color: 'papaya.600' },
  Severe: { bg: 'papaya.10', color: 'red.100' },
  'Don’t know': { bg: 'iris.100', color: 'iris.600' },
  "I don't know": { bg: 'iris.100', color: 'iris.600' },
  Low: { bg: 'beige.100', color: 'papaya.600' },
  High: { bg: 'papaya.10', color: 'red.100' },
  Chronic: { bg: 'beige.200', color: 'papaya.600' },
  Acute: { bg: 'iris.100', color: 'iris.600' },
} as const;
const LOCAL_STATUS_COLORS = {
  Inactive: { bg: 'fluentHealthComplementary.GrayBorder', color: 'fluentHealthText.100' },
  Active: { bg: 'mintGreen.50', color: 'mintGreen.500' },
} as const;
// Consolidated health data configuration
const healthSections: HealthSection[] = [
  {
    id: 'symptoms',
    title: 'Recent Symptoms (last 6 months)',
    icon: (
      <SymptomsIcon
        width="20px"
        height="20px"
      />
    ),
    data: [],
  },
  {
    id: 'conditions',
    title: 'Conditions',
    icon: (
      <ConditionIcon
        width="20px"
        height="20px"
      />
    ),
    data: [],
  },
  {
    id: 'medications',
    title: 'Ongoing Medications and Supplements',
    icon: (
      <MedicationSupplementsIcon
        width="20px"
        height="20px"
      />
    ),
    data: [],
  },
  {
    id: 'allergies',
    title: 'Active Allergies',
    icon: (
      <AllergiesIcon
        width="20px"
        height="20px"
      />
    ),
    data: [],
  },
  {
    id: 'reproductive',
    title: 'Reproductive Health',
    icon: (
      <ReproductiveHealthIcon
        width="20px"
        height="20px"
      />
    ),
    data: [],
  },
  {
    id: 'familyHistory',
    title: 'Family History',
    icon: (
      <FamilyHistoryIcon
        width="20px"
        height="20px"
      />
    ),
    data: [],
  },
  {
    id: 'lifestyle',
    title: 'Lifestyle Basics',
    icon: (
      <LifestyleBasicsIcon
        width="20px"
        height="20px"
      />
    ),
    data: [],
  },
];
// Reusable severity badge component
const SeverityBadge = React.memo(({ severity }: { severity: HealthData['severity'] }) => {
  if (!severity) return null;
  if (severity === "I don't know") return null;
  const colors = LOCAL_SEVERITY_COLORS[severity];
  return (
    <Box
      px={2}
      py={1}
      borderRadius="full"
      bg={colors?.bg}
      color={colors?.color}
      fontSize="xs"
      fontWeight="medium"
    >
      {severity}
    </Box>
  );
});
const StatusBadge = React.memo(({ status }: { status: HealthData['status'] }) => {
  if (!status) return null;

  const colors = LOCAL_STATUS_COLORS[status];
  return (
    <Box
      px={2}
      py={1}
      borderRadius="full"
      bg={colors?.bg}
      color={colors?.color}
      fontSize="xs"
      fontWeight="medium"
    >
      {status}
    </Box>
  );
});
// Reusable data item component
const DataItem = React.memo(({ data, showDivider }: { data: HealthData; showDivider: boolean }) => {
  return (
    <Box>
      <Flex
        justify="space-between"
        mb={1}
      >
        <Text
          fontSize="16px"
          fontStyle="normal"
          fontWeight="400"
          lineHeight="21px"
          letterSpacing="-0.32px"
          color="gray.500"
          align="left"
          wordBreak="break-word"
        >
          {getTruncatedName(data.name, data.severity, data.status)}
        </Text>
        <Flex gap="4px">
          <SeverityBadge severity={data.severity} />
          <StatusBadge status={data.status} />
        </Flex>
      </Flex>
      <Text
        fontSize="12px"
        fontStyle="normal"
        fontWeight="400"
        lineHeight="15px"
        letterSpacing="-0.24px"
        color="charcoal.70"
        align="left"
      >
        {data.startData} {data.startData && data.endData && `to ${data.endData}`}
      </Text>
      {showDivider && <Divider mt={2} />}
    </Box>
  );
});
const DataItemLB = React.memo(({ data }: { data: HealthData }) => {
  return (
    <Box
      borderRadius="10px"
      border="1px solid #FCE6C6"
      background="#FFFAF2"
      padding="12px"
    >
      {data.icon}
      <Flex
        mb={1}
        mt="4px"
      >
        <Text
          fontSize="16px"
          fontStyle="normal"
          fontWeight="400"
          lineHeight="21px"
          letterSpacing="-0.32px"
          color="gray.500"
          align="left"
          wordBreak="break-all"
        >
          {data.value}
        </Text>
      </Flex>
      <Text
        fontSize="12px"
        fontStyle="normal"
        fontWeight="400"
        lineHeight="15px"
        letterSpacing="-0.24px"
        color="charcoal.70"
        align="left"
      >
        {data.name}
      </Text>
    </Box>
  );
});
// Reusable section component
interface SectionProps {
  title: string;
  icon: React.ReactNode;
  children: React.ReactNode;
}

const Section = React.memo(({ title, icon, children }: SectionProps) => {
  return (
    <Flex
      direction="column"
      mb="20px"
    >
      <HStack
        spacing={2}
        mb={3}
      >
        {icon}
        <Text
          fontWeight="400"
          fontSize="16px"
          letterSpacing="-0.32px"
          lineHeight="22px"
          align="left"
        >
          {title}
        </Text>
      </HStack>
      <VStack
        spacing={4}
        align="stretch"
        p={3}
        borderRadius="lg"
        bg="linear-gradient(180deg, #FFF6E9 0%, #FFFFFF 100%)"
      >
        {children}
      </VStack>
    </Flex>
  );
});
const SectionLB = React.memo(({ title, icon, children }: SectionProps) => {
  return (
    <Flex
      direction="column"
      mb="20px"
    >
      <HStack
        spacing={2}
        mb={3}
      >
        {icon}
        <Text
          fontWeight="400"
          fontSize="16px"
          letterSpacing="-0.32px"
          lineHeight="22px"
          align="left"
        >
          {title}
        </Text>
      </HStack>
      <Grid
        templateColumns="repeat(2, 1fr)"
        gap={4}
      >
        {children}
      </Grid>
    </Flex>
  );
});

// Optimized ViewMore/ViewLess button component
const ViewMoreButton = React.memo(
  ({
    sectionId,
    isExpanded,
    onToggle,
  }: {
    sectionId: string;
    isExpanded: boolean;
    onToggle: (sectionId: string, shouldExpand: boolean) => void;
  }) => {
    const handleClick = React.useCallback(() => {
      onToggle(sectionId, !isExpanded);
    }, [sectionId, isExpanded, onToggle]);

    return (
      <Button
        size="sm"
        mt={2}
        variant="link"
        color="iris.500"
        onClick={handleClick}
      >
        {isExpanded ? 'View Less' : `View More`}
      </Button>
    );
  }
);

export default function HealthSnapshot() {
  const sharePatientId = localStorage.getItem('sharePatientId');
  const { patient } = useSharePatient();
  const snapshotData = useGetShareSnapshotData(sharePatientId);
  const [viewMoreSections, setViewMoreSections] = useState<string[]>([]);
  // Optimized data processing with error handling
  const {
    familyMemberHistoryData,
    lifestyleData,
    allergiesData,
    conditionsData,
    medicationsSupplementsData,
    reproductiveHealthData,
    symptomsData,
  } = useMemo(() => {
    const data = snapshotData?.data;
    if (!data) {
      return {
        familyMemberHistoryData: [],
        lifestyleData: null,
        allergiesData: [],
        conditionsData: [],
        medicationsSupplementsData: [],
        reproductiveHealthData: [],
        symptomsData: [],
      };
    }

    try {
      return {
        familyMemberHistoryData: data.FamilyMemberHistoryList
          ? extractFamilyMemberHistoryData(data.FamilyMemberHistoryList)
          : [],
        lifestyleData:
          data.LifestyleNutritionExercise?.length > 0 &&
          data.LifestyleNutritionDiet?.length > 0 &&
          data.LifestyleNutritionMentalHealthSleep?.length > 0 &&
          data.LifestyleNutritionAlcoholTobaccoCaffeine?.length > 0
            ? extractLifestyleData({
                answerListExercise: data.LifestyleNutritionExercise,
                answerListDiet: data.LifestyleNutritionDiet,
                answerListMentalHealthSleep: data.LifestyleNutritionMentalHealthSleep,
                answerListAlcoholTobaccoCaffeine: data.LifestyleNutritionAlcoholTobaccoCaffeine,
              })
            : null,
        allergiesData: data.Allergies ? formatAllergies(data.Allergies) : [],
        conditionsData: data.Conditions ? formatConditions(data.Conditions) : [],
        medicationsSupplementsData: data.MedicationsSupplements ? formatMedications(data.MedicationsSupplements) : [],
        reproductiveHealthData:
          data.ReproductiveHealth?.length > 0 ? formatReproductiveHealth(data.ReproductiveHealth) : [],
        symptomsData: data.Symptoms ? formatSymptoms(data.Symptoms) : [],
      };
    } catch (error) {
      console.error('Error processing health snapshot data:', error);
      return {
        familyMemberHistoryData: [],
        lifestyleData: null,
        allergiesData: [],
        conditionsData: [],
        medicationsSupplementsData: [],
        reproductiveHealthData: [],
        symptomsData: [],
      };
    }
  }, [snapshotData?.data]);

  // Optimized dynamic health sections creation
  const dynamicHealthSections = useMemo(() => {
    if (!Array.isArray(healthSections)) {
      console.warn('healthSections is not an array:', healthSections);
      return [];
    }

    // Map sections with real data efficiently
    return healthSections.map((section) => {
      const sectionDataMap: any = {
        symptoms: symptomsData,
        conditions: conditionsData,
        medications: medicationsSupplementsData,
        allergies: allergiesData,
        reproductive: reproductiveHealthData,
        familyHistory: familyMemberHistoryData,
        lifestyle: lifestyleData
          ? [
              {
                id: 'diet',
                name: 'Diet',
                value: lifestyleData.dietDescriptions || '-',
                icon: (
                  <DietIcon
                    width="16px"
                    height="16px"
                  />
                ),
              },
              {
                id: 'exercise',
                name: 'Exercise',
                value: lifestyleData.exerciseTimesPerWeek ? `${lifestyleData.exerciseTimesPerWeek}/week` : '-',
                icon: (
                  <ExerciseIcon
                    width="16px"
                    height="16px"
                  />
                ),
              },
              {
                id: 'sleep',
                name: 'Sleep',
                value: lifestyleData.sleepHours ? `${lifestyleData.sleepHours}/day` : '-',
                icon: (
                  <SleepIcon
                    width="16px"
                    height="16px"
                  />
                ),
              },
              {
                id: 'stress',
                name: 'Stress',
                value: lifestyleData.stressLevel ? `${lifestyleData.stressLevel}/10` : '-',
                icon: (
                  <StressIcon
                    width="16px"
                    height="16px"
                  />
                ),
              },
              {
                id: 'drinkingHabits',
                name: 'Drinking habits',
                value: lifestyleData.drinkingHabits ? `${lifestyleData.drinkingHabits} drinks/week` : '-',
                icon: (
                  <DrinkingHabitsIcon
                    width="16px"
                    height="16px"
                  />
                ),
              },
              {
                id: 'smokingHabits',
                name: 'Smoking habits',
                value: lifestyleData.smokingHabits || '-',
                icon: (
                  <SmokingHabitsIcon
                    width="16px"
                    height="16px"
                  />
                ),
              },
            ]
          : [],
      };

      return {
        ...section,
        data: sectionDataMap[section.id] || section.data || [],
      };
    });
  }, [lifestyleData]);

  // Memoize expensive calculations
  const hasData = useMemo(() => {
    if (!Array.isArray(dynamicHealthSections)) {
      console.warn('dynamicHealthSections is not an array:', dynamicHealthSections);
      return false;
    }
    return dynamicHealthSections.some((section) => section?.data?.length > 0);
  }, [dynamicHealthSections]);

  // Optimized sections rendering with proper dependencies
  const renderSections = useMemo(() => {
    if (!Array.isArray(dynamicHealthSections)) {
      console.warn('dynamicHealthSections is not an array in renderSections:', dynamicHealthSections);
      return [];
    }

    return dynamicHealthSections
      .filter((section) => section?.data?.length > 0)
      .filter((section) => {
        // Show reproductive health not for male patients
        if (section?.id === 'reproductive') {
          return patient?.gender !== 'sab:male';
        }
        // Show all other sections
        return true;
      })
      .map((section) => {
        const isExpanded = viewMoreSections.includes(section.id);
        const displayData = isExpanded ? section.data : section.data.slice(0, 3);
        const isLifestyle = section.id === 'lifestyle';
        return (
          <Box key={section.id}>
            {isLifestyle ? (
              <SectionLB
                title={section.title}
                icon={section.icon}
              >
                {section.data.map((item: any) => (
                  <DataItemLB
                    key={`${item.name}-items`}
                    data={item}
                  />
                ))}
              </SectionLB>
            ) : (
              <Section
                title={section.title}
                icon={section.icon}
              >
                {displayData.map((item: any, index: number) => (
                  <DataItem
                    key={`${item.name}-items`}
                    data={item}
                    showDivider={index < displayData.length - 1 || section.data.length > 3}
                  />
                ))}
                {section.data.length > 3 && (
                  <ViewMoreButton
                    sectionId={section.id}
                    isExpanded={viewMoreSections.includes(section.id)}
                    onToggle={(sectionId: string, shouldExpand: boolean) => {
                      setViewMoreSections((prev) =>
                        shouldExpand ? [...prev, sectionId] : prev.filter((id) => id !== sectionId)
                      );
                    }}
                  />
                )}
              </Section>
            )}
          </Box>
        );
      });
  }, [dynamicHealthSections, viewMoreSections]);

  if (!hasData) {
    return (
      <Flex direction="column">
        <Text
          textAlign="left"
          fontSize="13"
          textTransform="uppercase"
          fontWeight="medium"
          letterSpacing="1.56px"
          lineHeight="16px"
          mb="8px"
        >
          My Health Snapshot
        </Text>
        <Text
          textAlign="left"
          color="charcoal.60"
          fontSize="sm"
          letterSpacing="-.28px"
          pb="24px"
        >
          Your essential, shareable summary. Key conditions, allergies and medications at a glance—perfect for new
          appointments or quick reference.
        </Text>
        <EmptyState message="No health details added yet" />
      </Flex>
    );
  }

  return (
    <>
      <Text
        textAlign="left"
        fontSize="13"
        textTransform="uppercase"
        fontWeight="medium"
        letterSpacing="1.56px"
        lineHeight="16px"
        mb="8px"
      >
        My Health Snapshot
      </Text>
      <Text
        textAlign="left"
        color="charcoal.60"
        fontSize="sm"
        letterSpacing="-.28px"
        pb="24px"
      >
        Your essential, shareable summary. Key conditions, allergies and medications at a glance—perfect for new
        appointments or quick reference.
      </Text>
      <VStack
        spacing={6}
        align="stretch"
        p={4}
        bgGradient="linear(196deg, #FFF2DF 0%, #DADCFF 100%)"
        overflowY="auto"
      >
        {renderSections}
      </VStack>
    </>
  );
}
