import React from 'react';
import { <PERSON><PERSON>, <PERSON>, CardBody, Divider, Flex, Heading, Text } from '@chakra-ui/react';
import { FileText as FileIcon } from 'react-feather';

const openFile = (url: string) => {
  window.open(url, '_blank');
};

function ShareHealthInsurance({ healthInsurances }: { healthInsurances: any[] }) {
  if (healthInsurances.length === 0) return null;
  return (
    <Flex
      direction="column"
      gap="12px"
      mt="16px"
    >
      {healthInsurances.map((insurance: any) => {
        const organisation = insurance?.item.find((el: any) => {
          return el.linkId === 'health-insurance-provider-name';
        });
        const contactNumber = insurance?.item.find((el: any) => {
          return el.linkId === 'health-insurance-contact-number';
        });
        const policyNumber = insurance?.item.find((el: any) => {
          return el.linkId === 'health-insurance-policy-number';
        });
        const fileReference = insurance?.item.find((el: any) => {
          return el.linkId === 'health-insurance-reference';
        });

        return (
          <Flex
            bg="white"
            borderRadius="16px"
            key={insurance.id}
            direction="column"
            boxShadow="0px 2px 8px rgba(0, 0, 0, 0.1)"
          >
            <Card
              bgColor="periwinkle.50"
              boxShadow="none"
              borderRadius="8px"
            >
              <CardBody padding="12px">
                <Heading
                  fontSize="20px"
                  color="periwinkle.600"
                  fontWeight="400"
                  textTransform="capitalize"
                >
                  {organisation?.answer[0]?.valueCoding?.display || ''}
                </Heading>
              </CardBody>
            </Card>
            {policyNumber?.answer[0]?.valueString && (
              <Flex
                direction="column"
                padding="16px 20px"
                gap="8px"
              >
                <Text
                  fontSize="16px"
                  color="gray.300"
                  fontWeight="500"
                  letterSpacing="-0.32px"
                >
                  Policy number
                </Text>
                <Text
                  fontSize="16px"
                  color="gray.500"
                  letterSpacing="-0.32px"
                >
                  {policyNumber?.answer[0]?.valueString || ''}
                </Text>
              </Flex>
            )}
            {contactNumber?.answer[0]?.valueString && (
              <>
                <Divider
                  bg="gray.100"
                  w="90%"
                  mx="auto"
                />
                <Flex
                  direction="column"
                  padding="16px 20px"
                  gap="8px"
                >
                  <Text
                    fontSize="16px"
                    color="gray.300"
                    fontWeight="500"
                    letterSpacing="-0.32px"
                  >
                    Contact number
                  </Text>
                  <Text
                    fontSize="16px"
                    color="gray.500"
                    letterSpacing="-0.32px"
                  >
                    {'+91 '}
                    {contactNumber?.answer[0]?.valueString || ''}
                  </Text>
                </Flex>
              </>
            )}
            {fileReference?.answer?.[0]?.valueReference?.resource?.content?.[0]?.attachment?.url && (
              <>
                <Divider
                  bg="gray.100"
                  w="90%"
                  mx="auto"
                />
                <Flex
                  direction="column"
                  padding="16px 20px"
                  gap="2px"
                >
                  <Text
                    fontSize="16px"
                    color="gray.300"
                    fontWeight="500"
                    letterSpacing="-0.32px"
                  >
                    Files
                  </Text>
                  <Button
                    leftIcon={<FileIcon size={15} />}
                    variant="ghost"
                    size="md"
                    color="gray.500"
                    justifyContent="left"
                    padding={0}
                    borderRadius={0}
                    onClick={() =>
                      openFile(fileReference?.answer?.[0]?.valueReference?.resource?.content[0]?.attachment?.url)
                    }
                  >
                    <Text
                      color="gray.500"
                      maxW="150px"
                      overflow="hidden"
                      textOverflow="ellipsis"
                      whiteSpace="nowrap"
                      fontSize="14px"
                    >
                      {fileReference?.answer?.[0]?.valueReference?.resource?.content[0]?.attachment?.title}
                    </Text>
                  </Button>
                </Flex>
              </>
            )}
          </Flex>
        );
      })}
    </Flex>
  );
}

export default ShareHealthInsurance;
