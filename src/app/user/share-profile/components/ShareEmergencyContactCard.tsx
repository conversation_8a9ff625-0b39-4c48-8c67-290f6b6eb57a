import { Card, CardBody, Flex, Heading, Text } from '@chakra-ui/react';

import { parsePatientName, parsePhoneNo } from '@lib/utils/utils';
import { RelatedPerson } from 'src/gql/graphql';

function EmergencyContactsCard({ emergencyContacts }: { emergencyContacts: Related<PERSON>erson[] | null }) {
  if (!emergencyContacts?.length) {
    return (
      <Text
        color="iris.300"
        fontSize="13"
        fontWeight="medium"
        letterSpacing="1.56px"
        lineHeight="16px"
        mt="20px"
      >
        No emergency contacts available
      </Text>
    );
  }

  return (
    <Flex
      flexWrap="wrap"
      rowGap="20px"
      columnGap="20px"
      mt="20px"
    >
      {emergencyContacts.map((contact: any) => {
        return (
          <Card
            bgColor="fluentHealthSecondary.500"
            w="full"
            key={contact.id}
            display="flex"
            boxShadow="none"
            borderRadius="8px"
          >
            <CardBody padding="8px 16px 12px 16px">
              <Flex direction="column">
                <Heading
                  fontSize="20px"
                  textColor="periwinkle.700"
                  maxW="300px"
                  mt={1}
                  overflow="hidden"
                  textOverflow="ellipsis"
                  letterSpacing="-0.4px"
                  whiteSpace="nowrap"
                >
                  {parsePatientName(contact.name)}
                </Heading>
                {contact.relationship?.[1]?.coding?.[0]?.display && (
                  <Text
                    textColor="iris.300"
                    pt={1}
                    letterSpacing="-0.32px"
                  >
                    {contact.relationship[1].coding[0].display}
                  </Text>
                )}
              </Flex>

              {parsePhoneNo(contact?.telecom) && (
                <Flex
                  mt="4"
                  justifyContent="space-between"
                  alignItems="center"
                  height="28px"
                >
                  <Text
                    textColor="periwinkle.700"
                    letterSpacing="-0.32px"
                  >
                    {'+91 '}
                    {parsePhoneNo(contact.telecom)}
                  </Text>
                </Flex>
              )}
            </CardBody>
          </Card>
        );
      })}
    </Flex>
  );
}

export default EmergencyContactsCard;
