/* eslint-disable no-sequences */
/* eslint-disable @typescript-eslint/naming-convention */
import axios, { AxiosProgressEvent } from 'axios';
import { EmergencyContactPayLoad } from '@user/lib/models/emergency-contact';
import { ObservationNames } from '@src/types/observation';
import { addDays } from 'date-fns';
import { Buffer } from 'buffer';
import { CARE_TEAM_ACTION } from '@user/lib/constants';
import { useSearchParams } from 'react-router-dom';

import { FH_UI_CODESYSTEM } from 'src/app/medical-records/lib/constants';
import { QuestionnaireResponsePayload } from './models/questionnaire-response';
import {
  AllergyIntolerance,
  CareTeam,
  Condition,
  FamilyMemberHistory,
  Observation,
  Patient,
  Practitioner,
  Procedure,
  Questionnaire,
  QuestionnaireResponse,
  RelatedPerson,
} from 'src/gql/graphql';
import ApiError from '../../../components/error/ApiError';
import { AuthService } from '@lib/authService';
import {
  API_GATEWAY_URL,
  CMS_GRAPHQL_API_URL,
  FHIR_STRUCTURE_DEFINITION_URL,
  FHIR_VALUE_SET_FACT_URL,
  FHIR_VALUE_SET_URL,
  MEDPLUM_API_URL,
  MEDPLUM_API_URL_REST,
  MEDPLUM_CARE_TEAM_API_URL,
  MEDPLUM_GRAPHQL_API_URL,
  MEDPLUM_GRAPHQL_SHARE_API_URL,
  MEDPLUM_QUESTIONNAIRE,
  MED_PLUM_API_URL_REST,
  SNOMED_MEDICATION_FHIR,
} from '@lib/constants';
import { getAge } from 'src/utils/utils';
import { medplumGraphQlQuery } from './medplum-graphql-query';
import {
  BloodTypeObservationPayload,
  HealthcareProxy,
  ObservationPayload,
  PatientData,
  PatientDetails,
  PatientPayload,
} from './models/patient';
import { HealthcareProxyContactPayLoad } from './models/healthcare-proxy';
import { cleanPhoneNo, generateISO8601DateTime, generateUnixTimestamp, removeNullValues } from '@lib/utils/utils';
import {
  FHIR_HL7_CODE_OBSERVATION_CATEGORY,
  FHIR_HL7_CODE_SYSTEM_COMMUNICATION_CATEGORY,
  FH_CODE_SYSTEM_CREATED_AT,
  FH_CODE_SYSTEM_FLUENT_HEALTH_UI,
  SNOMED_URL,
} from 'src/constants/medplumConstants';
import { FamilyMemberHistoryPayload } from '@lib/models/family-member-history';
import { createAuditEventPayload } from './AuditEventPayload';
import { AnalyticsService } from '@lib/analyticsService';
import { IdentifyProviderNames, identifyHealthProfileUser } from '@lib/identifyService';
import { enumContentType } from 'src/app/medical-records/lib/state';
import { usePublicSettings } from '@lib/state';

const DELETE_QUESTIONNAIRE_TASK = 'urn:uuid:6aecda40-4b5b-4778-975e-9b6db4ae6bc8 ';
const DELETE_IMMUNIZATION_TASK = 'urn:uuid:6aecda40-4b5b-4778-975e-9b6db4ae6bc8 ';
const patientInfo = {
  async getPatientApi(patientId: Patient['id']) {
    const {
      data: { data },
    } = await axios.get(`${MEDPLUM_API_URL}/fhir/R4/Patient/${patientId}`, {
      headers: AuthService.instance.withAuthHeader(),
    });
    return data;
  },
  async get(patientId: Patient['id']) {
    const {
      data: { data },
    } = await axios.get(`${MEDPLUM_API_URL}/fhir/R4/Patient/${patientId}`, {
      headers: AuthService.instance.withAuthHeader(),
    });
    return data;
  },
  // async getPatientAvatar(id: string, patientId: Patient['id']) {
  //   const {
  //     data: { data },
  //   } = await axios.get(`${MEDPLUM_API_URL}/fhir/R4/Patient/${id}`, {
  //     headers: AuthService.instance.withAuthHeader(),
  //   });
  //   return data;
  // },

  async getPatient(patientId: Patient['id']) {
    const {
      data: { data },
      status,
    } = await axios.post(
      MEDPLUM_GRAPHQL_API_URL,
      {
        query: medplumGraphQlQuery.patientInfo.getPatient(patientId),
      },
      {
        headers: AuthService.instance.withAuthHeader(),
      }
    );
    const details: PatientDetails = data.Patient;
    if (status !== 200) {
      throw new ApiError(details);
    }
    if (details?.birthDate) {
      details.age = getAge(details?.birthDate);
    }
    if (details?.communication && details.communication.length) {
      const preferredLanguage: any = details?.communication?.filter((e) => e.preferred);
      details.preferred_language = preferredLanguage?.[0]?.language?.coding[0]?.display;
    }
    AnalyticsService.instance.identifyUser(details);

    return { data: details } as PatientData;
  },
  async getRecordsCompleted(patientId: Patient['id']) {
    const {
      data: { data },
    } = await axios.post(
      MEDPLUM_GRAPHQL_API_URL,
      {
        query: medplumGraphQlQuery.patientInfo.recordsCompleted(patientId),
      },
      {
        headers: AuthService.instance.withAuthHeader(),
      }
    );

    return data?.TaskList;
  },
  async updatePatient(patientId: Patient['id'], payload: any) {
    const { data, status } = await axios.put(
      `${MEDPLUM_API_URL}/fhir/R4/Patient/${patientId}`,
      removeNullValues(payload),
      {
        headers: AuthService.instance.withAuthHeader(),
      }
    );

    if (status !== 200) {
      throw new ApiError(data);
    }
    return data as { success: boolean };
  },

  async deletePatientData(patientId: Patient['id'], payload: PatientPayload) {
    const { data, status } = await axios.patch(`${MEDPLUM_API_URL}/fhir/R4/Patient/${patientId}`, payload, {
      headers: AuthService.instance.withAuthHeader(),
    });

    if (status !== 200) {
      throw new ApiError(data);
    }
    return data as { success: boolean };
  },

  async addPatientObservation(patientId: Patient['id'], payload: ObservationPayload) {
    // TODO - Explore alternative methods for constructing GraphQL payloads.
    const observationPayload: Observation = {
      resourceType: 'Observation',
      status: payload.status,
      code: {
        coding: [
          {
            system: payload.system,
            code: payload.code,
            display: payload.codeDisplay,
          },
        ],
      },
      category: [
        {
          coding: [
            {
              system: FHIR_HL7_CODE_OBSERVATION_CATEGORY,
              code: payload.categoryCode,
              display: payload.categoryDisplay,
            },
          ],
        },
      ],
      subject: {
        reference: `Patient/${patientId}`,
      },
      effectiveDateTime: generateISO8601DateTime(),
      valueQuantity:
        (payload.name === ObservationNames.Height || payload.name === ObservationNames.Weight) && payload?.unit
          ? {
              value: payload.value as number,
              unit: payload.unit,
              system: payload.unitSystem,
              code: payload.unit,
            }
          : undefined,
      valueCodeableConcept:
        payload.name === ObservationNames.BloodType
          ? {
              coding: [
                {
                  system: payload?.valueCodeUrl,
                  code: payload?.valueCode,
                  display: payload?.valueDisplay,
                },
              ],
              text: payload?.valueText,
            }
          : undefined,
    };
    const auditPayload: any = createAuditEventPayload({
      patientId: patientId ?? '',
      urnId: 'urn:uuid:b56cf8ab-258d-445d-92a8-d5a71a40cadc',
      display: payload.codeDisplay,
      currentValue: payload.status === 'cancelled' ? undefined : payload?.value?.toString() ?? '',
      code: payload.codeDisplay,
      previousValue: payload.previousValue,
    });
    const bundlePayload = {
      resourceType: 'Bundle',
      type: 'transaction',
      entry: [
        {
          fullUrl: 'urn:uuid:b56cf8ab-258d-445d-92a8-d5a71a40cadc',
          resource: observationPayload,
          request: {
            method: 'POST',
            url: 'Observation',
          },
        },
        {
          resource: auditPayload,
          request: {
            method: 'POST',
            url: 'AuditEvent',
          },
        },
      ],
    };

    const { data } = await axios.post(`${MEDPLUM_API_URL}/fhir/R4`, bundlePayload, {
      headers: AuthService.instance.withAuthHeader(),
    });
    data?.entry?.map((el: any) => {
      if (parseInt(el?.response?.status, 10) !== 201) throw new ApiError(data);
      return data as { success: boolean };
    });
  },

  async updatePatientBloodTypeObservation(
    observationId: Observation['id'],
    payload: BloodTypeObservationPayload,
    previousValue: string,
    patientId: Patient['id']
  ) {
    const bloodGroupAuditPayload = createAuditEventPayload({
      patientId: patientId ?? '',
      observationId: observationId ?? '',
      display: 'Blood Group',
      currentValue: payload.valueCodeableConceptText ?? undefined,
      code: 'blood group',
      previousValue,
    });

    const bundlePayload = {
      resourceType: 'Bundle',
      type: 'transaction',
      entry: [
        {
          resource: {
            resourceType: 'Observation',
            id: observationId,
            status: 'final',
            code: {
              coding: [
                {
                  system: 'http://loinc.org',
                  code: '883-9',
                  display: 'Blood Group',
                },
              ],
            },
            category: [
              {
                coding: [
                  {
                    system: 'http://terminology.hl7.org/CodeSystem/observation-category',
                    code: 'laboratory',
                    display: 'laboratory',
                  },
                ],
              },
            ],
            subject: {
              reference: `Patient/${patientId}`,
            },
            effectiveDateTime: generateISO8601DateTime(),
            valueCodeableConcept: {
              coding: [
                {
                  system: payload.valueCodeableConceptSystem,
                  code: payload.valueCodeableConceptCode,
                  display: payload.valueCodeableConceptDisplay,
                },
              ],
              text: payload.valueCodeableConceptText,
            },
          },
          request: {
            method: 'PUT',
            url: `Observation/${observationId}`,
          },
        },
        {
          resource: bloodGroupAuditPayload,
          request: {
            method: 'POST',
            url: 'AuditEvent',
          },
        },
      ],
    };

    const { data, status } = await axios.post(`${MEDPLUM_API_URL}/fhir/R4`, bundlePayload, {
      headers: AuthService.instance.withAuthHeader(),
    });

    if (status !== 200) throw new ApiError(data);
    return data as { success: boolean };
  },

  async updatePatientDetail(patientId: Patient['id'], payload: any, previousValue: string, isCleared?: boolean) {
    // Construct the GraphQL payload
    const updatePayload = [];
    let extensionAuditPayload;
    if (payload?.extension) {
      updatePayload.push({
        op: 'add',
        path: '/extension',
        value: payload?.extension,
      });
      ['PatientEthnicity', 'PatientGenderIdentity'].forEach((type) => {
        const url = `${FHIR_STRUCTURE_DEFINITION_URL}/${type}`;
        const extension = payload.extension?.find((obj: any) => obj.url === url);
        if (extension && extension.value) {
          extensionAuditPayload = createAuditEventPayload({
            patientId: patientId ?? '',
            observationId: patientId ?? '',
            display: type,
            code: type === 'PatientEthnicity' ? 'ethnicity' : 'Gender Identity',
            currentValue: extension.value,
            previousValue,
          });
        }
      });
    }

    if (payload?.patientGender) {
      updatePayload.push({
        op: 'add',
        path: '/gender',
        value: payload.patientGender,
      });
      if (payload?.patientGender !== previousValue) {
        extensionAuditPayload = createAuditEventPayload({
          patientId: patientId ?? '',
          observationId: patientId ?? '',
          display: 'gender',
          code: 'Sex assigned at birth',
          currentValue: payload?.patientGender?.split(':')[1] || payload?.patientGender || '',
          previousValue,
        });
      }
    }

    if (payload?.communication?.length) {
      updatePayload.push({
        op: 'add',
        path: '/communication',
        value: payload?.communication,
      });
      if (payload?.communication?.[0]?.language?.coding?.[0]?.display !== previousValue) {
        extensionAuditPayload = createAuditEventPayload({
          patientId: patientId ?? '',
          observationId: patientId ?? '',
          display: 'communication',
          code: 'communication',
          currentValue: payload?.communication?.[0]?.language?.coding?.[0]?.display ?? '',
          previousValue,
        });
      } else if (!payload?.communication?.[0]?.preferred) {
        extensionAuditPayload = createAuditEventPayload({
          patientId: patientId ?? '',
          observationId: patientId ?? '',
          display: 'communication',
          code: 'communication',
          currentValue: undefined,
          previousValue,
        });
      }
    }
    if (isCleared) {
      extensionAuditPayload = createAuditEventPayload({
        patientId: patientId ?? '',
        observationId: patientId ?? '',
        display: 'PatientEthnicity',
        code: 'ethnicity',
        currentValue: undefined,
        previousValue,
      });
    }
    // if there is no change return false
    if (!updatePayload?.length) return false;
    const bundlePayload = {
      resourceType: 'Bundle',
      type: 'transaction',
      entry: [
        {
          resource: extensionAuditPayload,
          request: {
            method: 'POST',
            url: 'AuditEvent',
          },
        },
      ],
    };
    const { data, status } = await axios.patch(`${MEDPLUM_API_URL}/fhir/R4/Patient/${patientId}`, updatePayload, {
      headers: AuthService.instance.withAuthHeader(),
    });
    await axios.post(`${MEDPLUM_API_URL}/fhir/R4`, bundlePayload, {
      headers: AuthService.instance.withAuthHeader(),
    });
    if (status !== 200) {
      throw new ApiError(data);
    }

    return data as { success: boolean };
  },

  async deletePatientObservation(patient: Patient, observationId: Observation['id'], previousValue: string) {
    const bloodGroupAuditPayload = createAuditEventPayload({
      patientId: patient.id ?? '',
      observationId: observationId ?? '',
      display: 'Blood Group',
      currentValue: undefined,
      code: 'blood group',
      previousValue,
    });
    const batchRequest = {
      resourceType: 'Bundle',
      type: 'batch',
      entry: [
        {
          request: {
            method: 'DELETE',
            url: `Observation/${observationId}`,
          },
        },
        {
          resource: bloodGroupAuditPayload,
          request: {
            method: 'POST',
            url: 'AuditEvent',
          },
        },
      ],
    };
    const response = await axios.post(`${MEDPLUM_API_URL}/fhir/R4`, batchRequest, {
      headers: AuthService.instance.withAuthHeader(),
    });

    if (response?.status !== 200) {
      throw new Error('Failed to delete contact.');
    }
    const success = response?.status === 200;
    return { success };
  },
  async getPatientSettingsAll(patientId: Patient['id']) {
    const {
      data: { data },
    } = await axios.post(
      MEDPLUM_GRAPHQL_API_URL,
      {
        query: medplumGraphQlQuery.patientInfo.getPatientSettingsAll(patientId),
      },
      {
        headers: AuthService.instance.withAuthHeader(),
      }
    );

    return data;
  },
  async updateBasicSettings(payload: any) {
    let data;
    if (payload?.basicId) {
      const updatePayload = [
        {
          op: 'add',
          path: payload.path,
          value: payload.value,
        },
      ];
      data = await axios.patch(`${MEDPLUM_API_URL}/fhir/R4/Basic/${payload?.basicId}`, updatePayload, {
        headers: AuthService.instance.withAuthHeader(),
      });
    } else {
      data = await axios.post(`${MEDPLUM_API_URL}/fhir/R4/Basic`, payload, {
        headers: AuthService.instance.withAuthHeader(),
      });
    }

    return data;
  },
  async updateBasicSettingsCommunication(payload: any) {
    try {
      const { data: basicResource } = await axios.get(`${MEDPLUM_API_URL}/fhir/R4/Basic/${payload.id}`, {
        headers: AuthService.instance.withAuthHeader(),
      });
      const settingsExt = basicResource.extension[0];
      const commPrefsExtIndex = settingsExt.extension.findIndex((ext: any) => ext.url === 'communicationPreferences');
      const commMarketingExtIndex = settingsExt.extension[commPrefsExtIndex].extension.findIndex(
        (ext: any) => ext.url === 'communicationMarketing'
      );
      const channelExtIndex = settingsExt.extension[commPrefsExtIndex].extension[
        commMarketingExtIndex
      ].extension.findIndex((ext: any) => ext.url === payload.url);
      const prefExtIndex = settingsExt.extension[commPrefsExtIndex].extension[commMarketingExtIndex].extension[
        channelExtIndex
      ].extension.findIndex((ext: any) => ext.url === 'preference');
      const path = `/extension/0/extension/${commPrefsExtIndex}/extension/${commMarketingExtIndex}/extension/${channelExtIndex}/extension/${prefExtIndex}/valueBoolean`;
      const patchPayload = [
        {
          op: 'replace',
          path,
          value: payload.value,
        },
      ];
      return await axios.patch(`${MEDPLUM_API_URL}/fhir/R4/Basic/${payload.id}`, patchPayload, {
        headers: { ...AuthService.instance.withAuthHeader(), 'Content-Type': 'application/json-patch+json' },
      });
    } catch (error: any) {
      console.error('Error in updateBasicSettingsCommunication:', error);
      throw error;
    }
  },
  async updatePatientSettings(patientId: Patient['id'], payload: any) {
    const updatePayload = [
      {
        op: 'replace',
        path: payload.path,
        value: payload.value,
      },
    ];
    // if there is no change return false
    if (!updatePayload?.length) return false;

    const { data, status } = await axios.patch(`${MEDPLUM_API_URL}/fhir/R4/Patient/${patientId}`, updatePayload, {
      headers: AuthService.instance.withAuthHeader(),
    });

    if (status !== 200) {
      throw new ApiError(data);
    }

    return data as { success: boolean };
  },
  // async updateProfilePhoto(patientId: string, payload: PatientPhotoPayload) {
  //   const { data, status } = await axios.post(
  //     `${MEDPLUM_API_URL}/fhir/R4/RelatedPerson`,
  //     {
  //       resourceType: 'RelatedPerson',
  //       patient: {
  //         reference: `Patient/${patientId}`,
  //       },
  //     },
  //     {
  //       headers: AuthService.instance.withAuthHeader(),
  //     }
  //   );

  //   if (status !== 200) {
  //     throw new ApiError(data);
  //   }

  //   return data;
  // },
};

const emergencyContact = {
  async getAll(patientId: Patient['id']) {
    const {
      data: { data },
      status,
    } = await axios.post(
      MEDPLUM_GRAPHQL_API_URL,
      {
        query: medplumGraphQlQuery.emergencyContact.getAll(patientId),
      },
      {
        headers: AuthService.instance.withAuthHeader(),
      }
    );
    const details = data.RelatedPersonList;
    if (status !== 200 || details.error === true) {
      throw new ApiError(details);
    }

    return details as RelatedPerson;
  },
  async addContact(patientId: Patient['id'], payload: EmergencyContactPayLoad) {
    // TODO - Explore alternative methods for constructing GraphQL payloads.
    const contactAddGraphqlPayload: RelatedPerson = {
      resourceType: 'RelatedPerson',
      name: [
        {
          family: payload?.lastName ?? '',
          given: [payload?.firstName ?? ''],
        },
      ],
      relationship: [
        {
          coding: [
            {
              system: 'http://terminology.hl7.org/CodeSystem/v2-0131',
              code: 'C',
              display: 'Emergency Contact',
            },
          ],
        },
      ],
      telecom: payload?.phoneNumber
        ? [{ system: 'phone', value: cleanPhoneNo(payload?.phoneNumber), use: 'home' }]
        : [],
      active: true,
      patient: {
        reference: `Patient/${patientId}`,
      },
      meta: {
        tag: [
          {
            system: FH_CODE_SYSTEM_CREATED_AT,
            code: generateUnixTimestamp().toString(),
            display: generateISO8601DateTime(),
          },
        ],
      },
    };
    if (payload?.relationCode && contactAddGraphqlPayload.relationship?.length) {
      contactAddGraphqlPayload.relationship.push({
        coding: [
          {
            system: 'http://terminology.hl7.org/CodeSystem/v3-RoleCode',
            code: payload?.relationCode ?? '',
            display: payload?.relationLabel ?? '',
          },
        ],
      });
    }

    const { data, status } = await axios.post(`${MEDPLUM_API_URL}/fhir/R4/RelatedPerson`, contactAddGraphqlPayload, {
      headers: AuthService.instance.withAuthHeader(),
    });
    if (status !== 201) throw new ApiError(data);
    return data as RelatedPerson;
  },
  async updateContact(contactId: RelatedPerson['id'], payload: EmergencyContactPayLoad) {
    // TODO - Explore alternative methods for constructing GraphQL payloads.
    const contactUpdateGraphqlPayload = [];
    if (payload?.firstName && payload?.lastName) {
      contactUpdateGraphqlPayload.push({
        op: 'replace',
        path: '/name',
        value: [
          {
            family: payload.lastName,
            given: [payload.firstName],
          },
        ],
      });
    }

    // Check if phoneNumber has been changed
    if (payload?.phoneNumber) {
      contactUpdateGraphqlPayload.push({
        op: 'replace',
        path: '/telecom',
        value: [
          {
            system: 'phone',
            value: cleanPhoneNo(payload.phoneNumber),
            use: 'home',
          },
        ],
      });
    }
    const emergencyContactDetail = {
      coding: [
        {
          system: 'http://terminology.hl7.org/CodeSystem/v2-0131',
          code: 'C',
          display: 'Emergency Contact',
        },
      ],
    };
    // Check if relation has been changed
    if (payload?.relationCode) {
      contactUpdateGraphqlPayload.push({
        op: 'replace',
        path: '/relationship',
        value: [
          {
            coding: [
              {
                system: 'http://terminology.hl7.org/CodeSystem/v3-RoleCode',
                code: payload?.relationCode ?? '',
                display: payload?.relationLabel ?? '',
              },
            ],
          },
          emergencyContactDetail,
        ],
      });
    } else {
      // If relation doesn't exist, only add Emergency Contact coding
      contactUpdateGraphqlPayload.push({
        op: 'replace',
        path: '/relationship',
        value: [emergencyContactDetail],
      });
    }
    const { data, status } = await axios.patch(
      `${MEDPLUM_API_URL}/fhir/R4/RelatedPerson/${contactId}`,
      contactUpdateGraphqlPayload,
      {
        headers: AuthService.instance.withAuthHeader(),
      }
    );
    if (status !== 200) throw new ApiError(data);
    return data as { success: boolean };
  },
  async deleteContact(contactId: string | number) {
    // TODO - Explore alternative methods for constructing DELETE payload
    const identifier = 'urn:fh-workflow:task:delete:emergency-contact';
    const relatedPersonResponseUpdate = Buffer.from(
      JSON.stringify([
        {
          op: 'add',
          path: '/meta/tag',
          value: [
            {
              system: FH_UI_CODESYSTEM,
              code: 'delete',
              display: 'Marked for deletion',
            },
          ],
        },
      ])
    ).toString('base64');

    const batchRequest = {
      resourceType: 'Bundle',
      type: 'transaction',
      entry: [
        {
          fullUrl: DELETE_QUESTIONNAIRE_TASK,
          resource: {
            resourceType: 'Task',
            status: 'requested',
            intent: 'option',
            priority: 'routine',
            identifier: [
              {
                value: identifier,
              },
            ],
            input: [
              {
                type: {
                  coding: [
                    {
                      code: identifier,
                    },
                  ],
                },
                valueReference: {
                  reference: `RelatedPerson/${contactId}`,
                },
              },
            ],
          },
          request: {
            method: 'POST',
            url: 'Task',
          },
        },
        {
          resource: {
            resourceType: 'Binary',
            contentType: 'application/json-patch+json',
            data: `{{${relatedPersonResponseUpdate}}}`,
          },
          request: {
            method: 'PATCH',
            url: `RelatedPerson/${contactId}`,
          },
        },
      ],
    };
    const response = await axios.post(`${MEDPLUM_API_URL}/fhir/R4`, batchRequest, {
      headers: AuthService.instance.withAuthHeader(),
    });

    if (response?.status !== 200) {
      throw new Error('Failed to delete contact.');
    }
    const success = response?.status === 200;
    return { success };
  },
};

const familyMemberHistory = {
  async getFamilyMemberHistoryList(patientId: Patient['id']) {
    const {
      data: { data },
      status,
    } = await axios.post(
      MEDPLUM_GRAPHQL_API_URL,
      {
        query: medplumGraphQlQuery.familyMemberHistory.getFamilyMemberData(patientId),
      },
      {
        headers: AuthService.instance.withAuthHeader(),
      }
    );
    identifyHealthProfileUser(IdentifyProviderNames.FamilyMemberHistory, data);
    identifyHealthProfileUser(IdentifyProviderNames.FamilyMemberHistoryConditionFlow, data);

    if (status !== 200) {
      throw new ApiError(data);
    }

    return data;
  },
  async addFamilyMemberHistory(patientId: Patient['id'], payload: FamilyMemberHistoryPayload) {
    const entry: any[] = [
      {
        fullUrl: 'urn:uuid:e16eac01-a5ee-4904-b1c8-f4bd56e338d5',
        resource: {
          resourceType: 'FamilyMemberHistory',
          identifier: [
            {
              system: 'http://acme.org/mrns',
              value: '013872',
            },
          ],
          status: 'partial',
          name: payload?.name,
          relationship: {
            coding: [
              {
                system: 'http://terminology.hl7.org/CodeSystem/v3-RoleCode',
                code: payload?.relationship?.value,
                display: payload?.relationship?.label,
              },
            ],
          },
          patient: {
            reference: `Patient/${patientId}`,
          },
        },
        request: {
          method: 'POST',
          url: 'FamilyMemberHistory',
        },
      },
    ];

    if (payload?.status !== undefined && payload?.status !== '') {
      entry[0].resource.deceasedBoolean = payload.status;
    }

    if (payload?.date_of_birth) {
      entry[0].resource.bornDate = payload.date_of_birth;
    }

    // Ensure `extension` is initialized as an empty array if not already present
    if (!entry[0].resource.extension) {
      entry[0].resource.extension = [];
    }

    // Conditionally add `ethnicity` extension
    if (payload?.ethnicity?.value) {
      entry[0].resource.extension.push({
        url: `${FHIR_VALUE_SET_URL}/FACT-eth`,
        valueCode: payload.ethnicity.value,
      });
    }

    // Conditionally add `blood_type` extension
    if (payload?.blood_type?.value) {
      entry[0].resource.extension.push({
        url: `${FHIR_VALUE_SET_URL}/FACT-Category-bg`,
        valueCode: payload.blood_type.value,
      });
    }

    const addingUpdatedPayload = {
      resourceType: 'Bundle',
      type: 'transaction',
      entry,
    };

    const { data, status } = await axios.post(`${MEDPLUM_API_URL}/fhir/R4`, addingUpdatedPayload, {
      headers: AuthService.instance.withAuthHeader(),
    });

    if (status !== 200) throw new ApiError(data);
    return data as RelatedPerson;
  },
  async addFamilyMemberHistoryCondition(payload: FamilyMemberHistoryPayload) {
    const { data, status } = await axios.put(`${MEDPLUM_API_URL}/fhir/R4/FamilyMemberHistory/${payload.id}`, payload, {
      headers: AuthService.instance.withAuthHeader(),
    });

    if (status !== 200) throw new ApiError(data);
    return data as RelatedPerson;
  },
  async updateFamilyMemberHistory(
    patientId: Patient['id'],
    familyMemberId: FamilyMemberHistory['id'],
    payload: FamilyMemberHistoryPayload
  ) {
    const updatedPayload: FamilyMemberHistoryPayload = {
      status: 'partial', // Always include 'status' as 'partial'
      patient: {
        reference: `Patient/${patientId}`, // Always include 'patient' reference
      },
      extension: [], // Initialize an extension array to hold optional extensions
    };
    updatedPayload.name = payload?.name;

    // Add the properties only if they have valid values
    if (payload?.resourceType) {
      updatedPayload.resourceType = payload.resourceType;
    }

    if (payload?.id) {
      updatedPayload.id = payload.id;
    }

    if (payload?.relationship?.value) {
      updatedPayload.relationship = {
        coding: [
          {
            system: 'http://terminology.hl7.org/CodeSystem/v3-RoleCode',
            code: payload.relationship.value,
            display: payload.relationship.label,
          },
        ],
      };
    }

    // Ensure extension is initialized
    updatedPayload.extension = updatedPayload.extension ?? [];

    if (payload?.date_of_birth) {
      updatedPayload.bornDate = payload.date_of_birth;
    }

    if (payload?.ethnicity?.value) {
      updatedPayload.extension.push({
        url: `${FHIR_VALUE_SET_URL}/FACT-eth`,
        valueCode: payload.ethnicity.value,
      });
    }

    if (payload?.blood_type?.value) {
      updatedPayload.extension.push({
        url: `${FHIR_VALUE_SET_URL}/FACT-Category-bg`,
        valueCode: payload.blood_type.value,
      });
    }

    if (typeof payload?.status === 'boolean') {
      updatedPayload.deceasedBoolean = payload.status;
    }
    if (payload.condition) {
      updatedPayload.condition = removeNullValues(payload.condition);
    }
    const { data, status } = await axios.put(
      `${MEDPLUM_API_URL}/fhir/R4/FamilyMemberHistory/${familyMemberId}`,
      updatedPayload,
      {
        headers: AuthService.instance.withAuthHeader(),
      }
    );
    if (status !== 200) throw new ApiError(data);
    return data as { success: boolean };
  },
  async deleteFamilyMemberHistory(familyMemberHistoryId: FamilyMemberHistory['id']) {
    const identifier = 'urn:fh-workflow:task:delete:family-history';
    const familyMemberHistoryResponseUpdate = Buffer.from(
      JSON.stringify([
        {
          op: 'add',
          path: '/meta/tag',
          value: [
            {
              system: FH_UI_CODESYSTEM,
              code: 'delete',
              display: 'Marked for deletion',
            },
          ],
        },
      ])
    ).toString('base64');
    const payload = {
      resourceType: 'Bundle',
      type: 'transaction',
      entry: [
        {
          fullUrl: DELETE_QUESTIONNAIRE_TASK,
          resource: {
            resourceType: 'Task',
            status: 'requested',
            intent: 'option',
            priority: 'routine',
            identifier: [
              {
                value: identifier,
              },
            ],
            input: [
              {
                type: {
                  coding: [
                    {
                      code: identifier,
                    },
                  ],
                },
                valueReference: {
                  reference: `FamilyMemberHistory/${familyMemberHistoryId}`,
                },
              },
            ],
          },
          request: {
            method: 'POST',
            url: 'Task',
          },
        },
        {
          resource: {
            resourceType: 'Binary',
            contentType: 'application/json-patch+json',
            data: `{{${familyMemberHistoryResponseUpdate}}}`,
          },
          request: {
            method: 'PATCH',
            url: `FamilyMemberHistory/${familyMemberHistoryId}`,
          },
        },
      ],
    };

    const response = await axios.post(`${MEDPLUM_API_URL}/fhir/R4`, payload, {
      headers: AuthService.instance.withAuthHeader(),
    });

    if (response?.status !== 200) {
      throw new Error('Failed to delete family member.');
    }
    const success = response?.status === 200;
    return { success };
  },
};

const healthcareProxy = {
  async getAll(patientId: Patient['id']) {
    const {
      data: { data },
      status,
    } = await axios.post(
      MEDPLUM_GRAPHQL_API_URL,
      {
        query: medplumGraphQlQuery.healthcareProxy.getAll(patientId),
      },
      {
        headers: AuthService.instance.withAuthHeader(),
      }
    );
    const details = data.RelatedPersonList;
    if (status !== 200 || details.error === true) {
      throw new ApiError(details);
    }

    return details as HealthcareProxy;
  },
  async addContact(patientId: Patient['id'], payload: HealthcareProxyContactPayLoad) {
    const { data, status } = await axios.post(`${MEDPLUM_API_URL}/fhir/R4`, payload, {
      headers: AuthService.instance.withAuthHeader(),
    });

    if (status !== 200 || data.error === true) {
      throw new ApiError(data);
    }

    return data as RelatedPerson;
  },

  async deleteFile(payload: HealthcareProxyContactPayLoad) {
    // const Authorization : AuthService.instance.withAuthHeader().Authorization;
    // const Accept : 'application/fhir+json';
    const { data, status } = await axios.post(`${MEDPLUM_API_URL}/fhir/R4`, payload, {
      headers: AuthService.instance.withAuthHeader(),
    });
    if (status !== 200) throw new ApiError(data);
    return data as { success: boolean };
  },

  async updateContact(contactId: RelatedPerson['id'], payload: HealthcareProxyContactPayLoad) {
    const proxyUpdateGraphqlPayload = [];
    if (payload?.firstName && payload?.lastName) {
      proxyUpdateGraphqlPayload.push({
        op: 'replace',
        path: '/name',
        value: [
          {
            family: payload.lastName,
            given: [payload.firstName],
          },
        ],
      });
    }
    if (payload?.phoneNumber) {
      proxyUpdateGraphqlPayload.push({
        op: 'replace',
        path: '/telecom',
        value: [
          {
            system: 'phone',
            value: payload.phoneNumber,
            use: 'home',
          },
        ],
      });
    }

    const proxyContactDetail = {
      coding: [
        {
          system: 'http://terminology.hl7.org/CodeSystem/v3-RoleCode',
          code: 'HPOWATT',
          display: 'healthcare power of attorney',
        },
      ],
    };
    if (payload?.relationCode) {
      proxyUpdateGraphqlPayload.push({
        op: 'replace',
        path: '/relationship',
        value: [
          {
            coding: [
              {
                system: 'http://terminology.hl7.org/CodeSystem/v3-RoleCode',
                code: payload?.relationCode ?? '',
                display: payload?.relationLabel ?? '',
              },
            ],
          },
          proxyContactDetail,
        ],
      });
    } else {
      // If relation doesn't exist, only add Emergency Contact coding
      proxyUpdateGraphqlPayload.push({
        op: 'replace',
        path: '/relationship',
        value: [proxyContactDetail],
      });
    }
    if (payload?.file) {
      await axios.post(`${MEDPLUM_API_URL}/fhir/R4`, payload.file, {
        headers: AuthService.instance.withAuthHeader(),
      });
    }
    const { data, status } = await axios.patch(
      `${MEDPLUM_API_URL}/fhir/R4/RelatedPerson/${contactId}`,
      proxyUpdateGraphqlPayload,
      {
        headers: AuthService.instance.withAuthHeader(),
      }
    );
    if (status !== 200) throw new ApiError(data);
    return data as { success: boolean };
  },

  async deleteContact(contactId: string) {
    const identifier = 'urn:fh-workflow:task:delete:healthcare-proxy';
    const relatedPersonResponseUpdate = Buffer.from(
      JSON.stringify([
        {
          op: 'add',
          path: '/meta/tag',
          value: [
            {
              system: FH_UI_CODESYSTEM,
              code: 'delete',
              display: 'Marked for deletion',
            },
          ],
        },
      ])
    ).toString('base64');

    const batchRequest = {
      resourceType: 'Bundle',
      type: 'transaction',
      entry: [
        {
          fullUrl: DELETE_QUESTIONNAIRE_TASK,
          resource: {
            resourceType: 'Task',
            status: 'requested',
            intent: 'option',
            priority: 'routine',
            identifier: [
              {
                value: identifier,
              },
            ],
            input: [
              {
                type: {
                  coding: [
                    {
                      code: identifier,
                    },
                  ],
                },
                valueReference: {
                  reference: `RelatedPerson/${contactId}`,
                },
              },
            ],
          },
          request: {
            method: 'POST',
            url: 'Task',
          },
        },
        {
          resource: {
            resourceType: 'Binary',
            contentType: 'application/json-patch+json',
            data: `{{${relatedPersonResponseUpdate}}}`,
          },
          request: {
            method: 'PATCH',
            url: `RelatedPerson/${contactId}`,
          },
        },
      ],
    };

    // console.log('delete', payload);
    const { data, status } = await axios.post(`${MEDPLUM_API_URL}/fhir/R4`, batchRequest, {
      headers: AuthService.instance.withAuthHeader(),
    });

    if (status !== 200 || data.error === true) {
      throw new ApiError(data);
    }

    return data as { success: boolean };
  },
};

const healthInsurance = {
  async getAll(patientId: Patient['id']) {
    const url = `${MEDPLUM_QUESTIONNAIRE}/HealthInsurance`;
    const {
      data: { data },
      status,
    } = await axios.post(
      MEDPLUM_GRAPHQL_API_URL,
      {
        query: medplumGraphQlQuery.healthInsurance.getAll(url, patientId),
      },
      {
        headers: AuthService.instance.withAuthHeader(),
      }
    );

    if (status !== 200) {
      throw new ApiError(data);
    }
    // console.log({ data });

    return data;
  },

  async addHealthInsurance(patientId: Patient['id'], payload: any) {
    const { data, status } = await axios.post(`${MEDPLUM_API_URL}/fhir/R4`, payload, {
      headers: AuthService.instance.withAuthHeader(),
    });
    if (status !== 200 || data.error === true) {
      throw new ApiError(data);
    }

    return data;
  },

  async deleteHealthInsurance(payload: any) {
    const response = await axios.post(`${MEDPLUM_API_URL}/fhir/R4`, payload, {
      headers: AuthService.instance.withAuthHeader(),
    });
    if (response?.status !== 200) {
      throw new Error('Failed to delete health insurance.');
    }
    const success = response?.status === 200;
    return { success };
  },

  async updateHealthInsurance(payload: any) {
    const response = await axios.post(`${MEDPLUM_API_URL}/fhir/R4`, payload, {
      headers: AuthService.instance.withAuthHeader(),
    });
    if (response?.status !== 200) {
      throw new Error('Failed to update health insurance.');
    }
    const success = response?.status === 200;
    return { success };
  },

  async getAllOrganization() {
    const {
      data: { data },
      status,
    } = await axios.post(
      MEDPLUM_GRAPHQL_API_URL,
      {
        query: medplumGraphQlQuery.healthInsurance.getAllOrganization(),
      },
      {
        headers: AuthService.instance.withAuthHeader(),
      }
    );

    if (status !== 200) {
      throw new ApiError(data);
    }

    return data;
  },
};

const masterQuestionnaire = {
  async getAllMasterQuestionnaireList(url: Questionnaire['url'], isPublicMode?: boolean, accessToken?: string) {
    const {
      data: { data },
      status,
    } = await axios.post(
      isPublicMode ? MEDPLUM_GRAPHQL_SHARE_API_URL : MEDPLUM_GRAPHQL_API_URL,
      {
        query: medplumGraphQlQuery.questionnaireList.getAll(url),
      },
      {
        headers:
          isPublicMode && accessToken && accessToken !== 'undefined'
            ? AuthService.instance.withAuthHeader({ Authorization: `Bearer ${accessToken}` })
            : AuthService.instance.withAuthHeader(),
      }
    );

    if (status !== 200) {
      throw new ApiError(data);
    }

    return data.QuestionnaireList;
  },
  async getAllMasterQuestionnaireResponsePSList(patientId: Patient['id']) {
    const {
      data: { data },
      status,
    } = await axios.post(
      MEDPLUM_GRAPHQL_API_URL,
      {
        query: medplumGraphQlQuery.questionnaireResponsePSList.getAll(patientId),
      },
      {
        headers: AuthService.instance.withAuthHeader(),
      }
    );

    if (status !== 200) {
      throw new ApiError(data);
    }

    return data;
  },
  async getAllMasterQuestionnaireResponseList(
    url: Questionnaire['url'],
    patientId: Patient['id'],
    isPublicMode?: boolean,
    accessToken?: string
  ) {
    const {
      data: { data },
      status,
    } = await axios.post(
      isPublicMode ? MEDPLUM_GRAPHQL_SHARE_API_URL : MEDPLUM_GRAPHQL_API_URL,
      {
        query: medplumGraphQlQuery.questionnaireResponseList.getAll(url, patientId),
      },
      {
        headers:
          isPublicMode && accessToken && accessToken !== 'undefined'
            ? AuthService.instance.withAuthHeader({ Authorization: `Bearer ${accessToken}` })
            : AuthService.instance.withAuthHeader(),
      }
    );
    // identify Health Profile User
    if (url === `${MEDPLUM_QUESTIONNAIRE}/ReproductiveHealth`) {
      identifyHealthProfileUser(IdentifyProviderNames.ReproductiveHealth, data?.QuestionnaireResponseList);
    }
    if (status !== 200) {
      throw new ApiError(data);
    }

    return data.QuestionnaireResponseList;
  },
  async addQuestionnaireResponse(patientId: Patient['id'], payloadData: QuestionnaireResponsePayload) {
    const payloadQuestionnaireResponse = {
      resourceType: 'QuestionnaireResponse',
      status: payloadData.status ?? 'active',
      authored: new Date(),
      item: payloadData.item,
      questionnaire: payloadData.questionnaire,
      subject: {
        reference: `Patient/${patientId}`,
      },
    };
    const entry = [];
    entry.push({
      fullUrl: 'urn:uuid:b9606b4f-78bd-410c-aa99-61843b6b42d8',
      resource: payloadQuestionnaireResponse,
      request: {
        method: 'POST',
        url: 'QuestionnaireResponse',
      },
    });
    if (payloadData?.linkageToList?.length) {
      payloadData?.linkageToList.forEach((linkData: any) => {
        entry.push({
          resource: {
            resourceType: 'Linkage',
            item: [
              linkData,
              {
                type: 'alternate',
                resource: {
                  reference: 'urn:uuid:b9606b4f-78bd-410c-aa99-61843b6b42d8',
                },
              },
            ],
          },
          request: {
            method: 'POST',
            url: 'Linkage',
          },
        });
      });
    }
    const payload = {
      resourceType: 'Bundle',
      type: 'transaction',
      entry,
    };
    const response = await axios.post(`${MEDPLUM_API_URL}/fhir/R4`, payload, {
      headers: AuthService.instance.withAuthHeader(),
    });

    if (response?.status !== 200) {
      throw new Error('Failed to add.');
    }
    return response;
  },
  async updateQuestionnaireResponse(patientId: Patient['id'], payloadData: QuestionnaireResponsePayload) {
    const payloadQuestionnaireResponse = {
      resourceType: 'QuestionnaireResponse',
      status: payloadData.status ?? 'active',
      item: payloadData.item,
      id: payloadData.id,
      questionnaire: payloadData.questionnaire,
      subject: {
        reference: `Patient/${patientId}`,
      },
    };
    const entry = [];
    entry.push({
      resource: payloadQuestionnaireResponse,
      request: {
        method: 'PUT',
        url: `QuestionnaireResponse/${payloadData.id}`,
      },
    });
    if (payloadData?.linkageToList?.length) {
      payloadData?.linkageToList.forEach((linkData: any) => {
        entry.push({
          resource: {
            resourceType: 'Linkage',
            item: [
              linkData,
              {
                type: 'alternate',
                resource: {
                  reference: `QuestionnaireResponse/${payloadData?.id}`,
                },
              },
            ],
          },
          request: {
            method: 'POST',
            url: 'Linkage',
          },
        });
      });
    }
    const payload = {
      resourceType: 'Bundle',
      type: 'transaction',
      entry,
    };
    const response = await axios.post(`${MEDPLUM_API_URL}/fhir/R4`, payload, {
      headers: AuthService.instance.withAuthHeader(),
    });

    if (response?.status !== 200) {
      throw new Error('Failed to update.');
    }
    const success = response?.status === 200;
    return { success };
  },

  async patchQuestionnaireResponseStatus(
    patientId: Patient['id'],
    questionnaireId: string,
    status: string,
    existingData: any
  ) {
    // Clean the item data to remove null values and invalid properties
    const cleanedItems = (existingData.item || []).map((item: any) => {
      const cleanedItem: any = {
        linkId: item.linkId,
      };

      // Only add text if it exists and is not null
      if (item.text && item.text !== null) {
        cleanedItem.text = item.text;
      }

      // Clean the answer array
      if (item.answer && Array.isArray(item.answer)) {
        cleanedItem.answer = item.answer
          .map((answer: any) => {
            const cleanedAnswer: any = {};

            // Only include the relevant value type, exclude null values
            if (answer.valueString !== null && answer.valueString !== undefined) {
              cleanedAnswer.valueString = answer.valueString;
            }
            if (answer.valueInteger !== null && answer.valueInteger !== undefined) {
              cleanedAnswer.valueInteger = answer.valueInteger;
            }
            if (answer.valueDecimal !== null && answer.valueDecimal !== undefined) {
              cleanedAnswer.valueDecimal = answer.valueDecimal;
            }
            if (answer.valueBoolean !== null && answer.valueBoolean !== undefined) {
              cleanedAnswer.valueBoolean = answer.valueBoolean;
            }
            if (answer.valueDate !== null && answer.valueDate !== undefined) {
              cleanedAnswer.valueDate = answer.valueDate;
            }
            if (answer.valueTime !== null && answer.valueTime !== undefined) {
              cleanedAnswer.valueTime = answer.valueTime;
            }
            if (answer.valueCoding !== null && answer.valueCoding !== undefined) {
              // Clean valueCoding to remove null system values
              const cleanedValueCoding: any = {};
              if (answer.valueCoding.code !== null && answer.valueCoding.code !== undefined) {
                cleanedValueCoding.code = answer.valueCoding.code;
              }
              if (answer.valueCoding.display !== null && answer.valueCoding.display !== undefined) {
                cleanedValueCoding.display = answer.valueCoding.display;
              }
              if (
                answer.valueCoding.system !== null &&
                answer.valueCoding.system !== undefined &&
                answer.valueCoding.system !== ''
              ) {
                cleanedValueCoding.system = answer.valueCoding.system;
              }

              // Only include valueCoding if it has at least code or display
              if (cleanedValueCoding.code || cleanedValueCoding.display) {
                cleanedAnswer.valueCoding = cleanedValueCoding;
              }
            }
            if (answer.valueReference !== null && answer.valueReference !== undefined) {
              cleanedAnswer.valueReference = answer.valueReference;
            }

            return cleanedAnswer;
          })
          .filter((answer: any) => Object.keys(answer).length > 0); // Remove empty answer objects
      }

      return cleanedItem;
    });

    const payloadQuestionnaireResponse = {
      resourceType: 'QuestionnaireResponse',
      status,
      item: cleanedItems,
      id: questionnaireId,
      questionnaire: existingData.questionnaire,
      subject: {
        reference: `Patient/${patientId}`,
      },
    };

    const payload = {
      resourceType: 'Bundle',
      type: 'transaction',
      entry: [
        {
          resource: payloadQuestionnaireResponse,
          request: {
            method: 'PUT',
            url: `QuestionnaireResponse/${questionnaireId}`,
          },
        },
      ],
    };

    const response = await axios.post(`${MEDPLUM_API_URL}/fhir/R4`, payload, {
      headers: AuthService.instance.withAuthHeader(),
    });

    if (response?.status !== 200) {
      throw new Error('Failed to patch status.');
    }
    return { success: response?.status === 200 };
  },

  async updateQuestionnaireResponseAsBundleForConditionDownloadShare(patientId: Patient['id'], payloadData: any) {
    const finalPayload = payloadData.map((value: any) => {
      return {
        resource: {
          resourceType: 'QuestionnaireResponse',
          id: value?.id,
          status: 'active',
          item: value?.item,
          questionnaire: `${MEDPLUM_QUESTIONNAIRE}/Condition`,
          subject: {
            reference: `Patient/${patientId}`,
          },
        },
        request: {
          method: 'PUT',
          url: `QuestionnaireResponse/${value?.id}`,
        },
      };
    });

    const payload = {
      resourceType: 'Bundle',
      type: 'transaction',
      entry: finalPayload,
    };
    const response = await axios.post(`${MEDPLUM_API_URL}/fhir/R4`, payload, {
      headers: AuthService.instance.withAuthHeader(),
    });

    if (response?.status !== 200) {
      throw new Error('Failed to update.');
    }
    const success = response?.status === 200;
    return { success };
  },
  async deleteQuestionnaireResponse(questionnaireId: QuestionnaireResponse['id']) {
    const batchRequest = {
      resourceType: 'Bundle',
      type: 'batch',
      entry: [
        {
          request: {
            method: 'DELETE',
            url: `QuestionnaireResponse/${questionnaireId}`,
          },
        },
      ],
    };
    const response = await axios.post(`${MEDPLUM_API_URL}/fhir/R4`, batchRequest, {
      headers: AuthService.instance.withAuthHeader(),
    });

    if (response?.status !== 200) {
      throw new Error('Failed to delete contact.');
    }
    const success = response?.status === 200;
    return { success };
  },

  async deleteQuestionnaireResponseTask(payload: any) {
    const { questionnaireId, identifier } = payload || {};

    const questionnaireResponseUpdate = Buffer.from(
      JSON.stringify([
        {
          op: 'add',
          path: '/meta/tag',
          value: [
            {
              system: FH_UI_CODESYSTEM,
              code: 'delete',
              display: 'Marked for deletion',
            },
          ],
        },
      ])
    ).toString('base64');

    const batchRequest = {
      resourceType: 'Bundle',
      type: 'transaction',
      entry: [
        {
          fullUrl: DELETE_QUESTIONNAIRE_TASK,
          resource: {
            resourceType: 'Task',
            status: 'requested',
            intent: 'option',
            priority: 'routine',
            identifier: [
              {
                value: identifier,
              },
            ],
            input: [
              {
                type: {
                  coding: [
                    {
                      code: identifier,
                    },
                  ],
                },
                valueReference: {
                  reference: `QuestionnaireResponse/${questionnaireId}`,
                },
              },
            ],
          },
          request: {
            method: 'POST',
            url: 'Task',
          },
        },
        {
          resource: {
            resourceType: 'Binary',
            contentType: 'application/json-patch+json',
            data: `{{${questionnaireResponseUpdate}}}`,
          },
          request: {
            method: 'PATCH',
            url: `QuestionnaireResponse/${questionnaireId}`,
          },
        },
      ],
    };
    const response = await axios.post(`${MEDPLUM_API_URL}/fhir/R4`, batchRequest, {
      headers: AuthService.instance.withAuthHeader(),
    });

    if (response?.status !== 200) {
      throw new Error('Failed to delete contact.');
    }
    const success = response?.status === 200;
    return { success };
  },
  // Get Conditions
  async getAllMasterConditionList(patientId: Patient['id'], isPublicMode: boolean, accessToken?: any) {
    const {
      data: { data },
      status,
    } = await axios.post(
      isPublicMode ? MEDPLUM_GRAPHQL_SHARE_API_URL : MEDPLUM_GRAPHQL_API_URL,
      {
        query: medplumGraphQlQuery.condition.getAll(patientId),
      },
      {
        headers: isPublicMode
          ? AuthService.instance.withAuthHeader({ Authorization: `Bearer ${accessToken}` })
          : AuthService.instance.withAuthHeader(),
      }
    );

    identifyHealthProfileUser(IdentifyProviderNames.ConditionFlow, data?.ConditionList);
    if (status !== 200) {
      throw new ApiError(data);
    }
    return data?.ConditionList || [];
  },
  // Add Conditions
  async addConditions(patientId: Patient['id'], payloadData: Condition) {
    try {
      const { data, status } = await axios.post(`${MED_PLUM_API_URL_REST}/Condition`, payloadData, {
        headers: AuthService.instance.withAuthHeader(),
      });

      if ((status !== 200 && status !== 201) || data.error) {
        throw new ApiError(data);
      }

      return data || {};
    } catch (error) {
      throw new Error('Something went wrong while adding the condition');
    }
  },
  async updateConditions(patientId: Patient['id'], payloadCondition: Condition, conditionId: string) {
    try {
      const { data, status } = await axios.put(`${MED_PLUM_API_URL_REST}/Condition/${conditionId}`, payloadCondition, {
        headers: AuthService.instance.withAuthHeader(),
      });

      if (status !== 200 || data.error) {
        throw new ApiError(data);
      }

      return data || {};
    } catch (error) {
      throw new Error('Something went wrong while updating the condition');
    }
  },
  async deleteCondition(conditionId: any) {
    const identifier = 'urn:fh-workflow:task:delete:condition';
    const conditionResponseUpdate = Buffer.from(
      JSON.stringify([
        {
          op: 'add',
          path: '/meta/tag',
          value: [
            {
              system: FH_UI_CODESYSTEM,
              code: 'delete',
              display: 'Marked for deletion',
            },
          ],
        },
      ])
    ).toString('base64');
    const payload = {
      resourceType: 'Bundle',
      type: 'transaction',
      entry: [
        {
          fullUrl: DELETE_QUESTIONNAIRE_TASK,
          resource: {
            resourceType: 'Task',
            status: 'requested',
            intent: 'option',
            priority: 'routine',
            identifier: [
              {
                value: identifier,
              },
            ],
            input: [
              {
                type: {
                  coding: [
                    {
                      code: identifier,
                    },
                  ],
                },
                valueReference: {
                  reference: `Condition/${conditionId}`,
                },
              },
            ],
          },
          request: {
            method: 'POST',
            url: 'Task',
          },
        },
        {
          resource: {
            resourceType: 'Binary',
            contentType: 'application/json-patch+json',
            data: `{{${conditionResponseUpdate}}}`,
          },
          request: {
            method: 'PATCH',
            url: `Condition/${conditionId}`,
          },
        },
      ],
    };
    const response = await axios.post(`${MEDPLUM_API_URL}/fhir/R4`, payload, {
      headers: AuthService.instance.withAuthHeader(),
    });

    if (response?.status !== 200) {
      throw new Error('Failed to delete condition.');
    }
    const success = response?.status === 200;
    return { success };
  },
};

const vaccineQuestionnaire = {
  async getAllVaccineQuestionnaireResponseList(patientId: Patient['id']) {
    const [
      ImmunizationCovid19,
      ImmunizationFlu,
      ImmunizationHepatitisA,
      ImmunizationHepatitisB,
      ImmunizationMeaslesMumpsRubellaMMR,
      ImmunizationTuberculosis,
      ImmunizationPneumonia,
      ImmunizationTDap,
      ImmunizationVaricella,
      ImmunizationShingles,
      ImmunizationHPV,
    ] = await Promise.all([
      masterQuestionnaire.getAllMasterQuestionnaireResponseList(
        `${MEDPLUM_QUESTIONNAIRE}/ImmunizationCovid19`,
        patientId
      ),
      masterQuestionnaire.getAllMasterQuestionnaireResponseList(`${MEDPLUM_QUESTIONNAIRE}/ImmunizationFlu`, patientId),
      masterQuestionnaire.getAllMasterQuestionnaireResponseList(
        `${MEDPLUM_QUESTIONNAIRE}/ImmunizationHepatitisA`,
        patientId
      ),
      masterQuestionnaire.getAllMasterQuestionnaireResponseList(
        `${MEDPLUM_QUESTIONNAIRE}/ImmunizationHepatitisB`,
        patientId
      ),
      masterQuestionnaire.getAllMasterQuestionnaireResponseList(
        `${MEDPLUM_QUESTIONNAIRE}/ImmunizationMeaslesMumpsRubellaMMR`,
        patientId
      ),
      masterQuestionnaire.getAllMasterQuestionnaireResponseList(
        `${MEDPLUM_QUESTIONNAIRE}/ImmunizationTuberculosis`,
        patientId
      ),
      masterQuestionnaire.getAllMasterQuestionnaireResponseList(
        `${MEDPLUM_QUESTIONNAIRE}/ImmunizationPneumonia`,
        patientId
      ),
      masterQuestionnaire.getAllMasterQuestionnaireResponseList(`${MEDPLUM_QUESTIONNAIRE}/ImmunizationTDap`, patientId),
      masterQuestionnaire.getAllMasterQuestionnaireResponseList(
        `${MEDPLUM_QUESTIONNAIRE}/ImmunizationVaricella`,
        patientId
      ),
      masterQuestionnaire.getAllMasterQuestionnaireResponseList(
        `${MEDPLUM_QUESTIONNAIRE}/ImmunizationShingles`,
        patientId
      ),
      masterQuestionnaire.getAllMasterQuestionnaireResponseList(`${MEDPLUM_QUESTIONNAIRE}/ImmunizationHPV`, patientId),
    ]);

    return {
      ImmunizationCovid19,
      ImmunizationFlu,
      ImmunizationVaricella,
      ImmunizationTuberculosis,
      ImmunizationHepatitisB,
      ImmunizationHepatitisA,
      ImmunizationPneumonia,
      ImmunizationMeaslesMumpsRubellaMMR,
      ImmunizationTDap,
      ImmunizationShingles,
      ImmunizationHPV,
    };
  },
};

const careTeam = {
  async getAll(patientId: Patient['id']) {
    const {
      data: { data },
      status,
    } = await axios.post(
      MEDPLUM_GRAPHQL_API_URL,
      { query: medplumGraphQlQuery.careTeam.getAll(patientId) },
      { headers: AuthService.instance.withAuthHeader() }
    );

    if (status !== 200) {
      throw new ApiError(data);
    }
    return data.CareTeamList as CareTeam[];
  },
  async create(patientId: Patient['id'], formValues: any) {
    const batchRequest = {
      resourceType: 'CareTeam',
      name: formValues.fullname,

      participant: [
        {
          member: {
            reference: `Practitioner/${formValues.id}`,
            display: formValues.fullname,
          },
        },
      ],
      subject: {
        reference: `Patient/${patientId}`,
        display: formValues.fullname,
      },
      managingOrganization: [
        {
          reference: `Organization/ec0b3664-6697-40c6-a1b6-7ad8ef2e6943`,
          display: 'Fluent Health',
        },
      ],
    };

    const response = await axios.post(MEDPLUM_CARE_TEAM_API_URL, batchRequest, {
      headers: AuthService.instance.withAuthHeader(),
    });

    if (response?.status !== 201) {
      throw new Error('Failed to create Care Team.');
    }
    if (formValues?.is_primary) {
      await careTeam.addCareAsPrimary(patientId, formValues.id, formValues.fullname);
    }
    return { success: response?.status === 201 };
  },

  async edit(patientId: Patient['id'], formValues: any) {
    const batchRequest = [
      {
        op: 'add',
        path: '/participant',
        value: [{ member: { reference: `Practitioner/${formValues.id}`, display: formValues.fullname } }],
      },
    ];

    const response = await axios.patch(`${MEDPLUM_CARE_TEAM_API_URL}/${formValues.subMember?.id}`, batchRequest, {
      headers: AuthService.instance.withAuthHeader(),
    });

    if (response?.status !== 200) {
      throw new Error('Failed to create Care Team.');
    }
    if (formValues?.is_primary) {
      await careTeam.addCareAsPrimary(patientId, formValues.id, formValues.fullname);
    }
    if (!formValues?.is_primary && formValues?.is_primary !== formValues.subMember.is_primary) {
      await careTeam.removeCareAsPrimary(patientId);
    }
    return { success: response?.status === 200 };
  },
  async addCareAsPrimary(patientId: Patient['id'], PractitionerId: Practitioner['id'], fullname: string) {
    const myRequest = [
      {
        op: 'add',
        path: '/generalPractitioner',
        value: [{ reference: `Practitioner/${PractitionerId}`, id: PractitionerId, display: fullname }],
      },
    ];

    const response = await axios.patch(`${MEDPLUM_API_URL}/fhir/R4/Patient/${patientId}`, myRequest, {
      headers: AuthService.instance.withAuthHeader(),
    });

    if (response.status !== 200) {
      throw new Error('Failed to create Care Team.');
    }
    // const {patient } = usePatient(patientId)
    // TODO Patch
    return { success: response.status === 200 };
  },
  async removeCareAsPrimary(patientId: Patient['id']) {
    const myRequest = [
      {
        op: 'remove',
        value: null,
        path: '/generalPractitioner',
      },
    ];

    const response = await axios.patch(`${MEDPLUM_API_URL}/fhir/R4/Patient/${patientId}`, myRequest, {
      headers: AuthService.instance.withAuthHeader(),
    });

    if (response.status !== 200) {
      throw new Error('Failed to create Care Team.');
    }
    return { success: response.status === 200 };
  },
  async remove(patientId: Patient['id'], formValues: any, careTeamList: any) {
    const practitionerId = formValues?.member?.resource?.id;
    const careTeamId = careTeamList?.[0]?.id;

    if (!practitionerId) throw new Error('Practitioner ID is missing.');
    if (!careTeamId) throw new Error('Care Team ID is missing.');

    const requestPayload = {
      resourceType: 'Bundle',
      type: 'transaction',
      entry: [
        {
          request: { method: 'POST', url: 'Task' },
          resource: {
            resourceType: 'Task',
            status: 'requested',
            intent: 'unknown',
            identifier: [{ value: `urn:fh-workflow:task:${CARE_TEAM_ACTION.DELETE_REFERENCE}` }],
            code: {
              coding: [{ display: 'Delete CareTeam Practitioner Reference', code: CARE_TEAM_ACTION.DELETE_REFERENCE }],
            },
            input: [
              {
                type: { coding: [{ display: 'careTeamId', code: 'careTeamId' }] },
                valueReference: { reference: `CareTeam/${careTeamId}` },
              },
              {
                type: { coding: [{ display: 'practitionerId', code: 'practitionerId' }] },
                valueReference: { reference: `Practitioner/${practitionerId}` },
              },
            ],
            for: { type: 'Patient', reference: `Patient/${patientId}` },
          },
        },
      ],
    };

    const response = await axios.post(`${MEDPLUM_API_URL}/fhir/R4`, requestPayload, {
      headers: AuthService.instance.withAuthHeader(),
    });

    if (response.status !== 200) throw new Error('Failed to remove Care Team member.');
    return { success: true };
  },
  async addNonFluent(patientId: Patient['id'], formValues: any, careTeamList: any) {
    const practitionerId = `urn:uuid:${CARE_TEAM_ACTION.NON_FLUENT_PRACTITIONER_ID}`;
    const CareTeamID = careTeamList?.[0]?.id;
    const previousPractioner = careTeamList.flatMap(({ participant }: any) =>
      participant?.map(({ member: { reference, resource } }: any) => ({
        member: {
          reference,
          display: resource?.name
            ? `${resource.name[0]?.given?.[0] ?? ''} ${resource.name[0]?.family ?? ''}`.trim()
            : 'Fluent Practitioner',
        },
      }))
    );

    const myRequest = {
      resourceType: 'Bundle',
      type: 'transaction',
      entry: [
        {
          fullUrl: practitionerId,
          resource: {
            resourceType: 'Practitioner',
            name: [{ given: [formValues.firstName], family: formValues.lastName }],
            telecom: [
              { system: 'email', use: 'work', value: formValues.email },
              { system: 'phone', use: 'mobile', value: formValues.phoneNumber },
              { system: 'phone', use: 'other', value: formValues.altPhoneNumber },
            ],
            address: [{ city: formValues.city, country: formValues.country }],
            meta: {
              account: { reference: `Patient/${patientId}` },
            },
          },
          request: { method: 'POST', url: 'Practitioner' },
        },
        {
          resource: {
            resourceType: 'PractitionerRole',
            practitioner: {
              reference: practitionerId,
              display: `${formValues.firstName} ${formValues.lastName}`,
            },
            active: true,
            period: { start: new Date().toISOString() },
            specialty: [
              {
                coding: [
                  {
                    system: SNOMED_URL,
                    code: formValues.speciality,
                    display: formValues.speciality.replace('sty:', '').toUpperCase(),
                  },
                ],
              },
            ],
          },
          request: { method: 'POST', url: 'PractitionerRole' },
        },
        {
          resource: {
            resourceType: 'CareTeam',
            id: CareTeamID,
            name: 'My Care Team',
            subject: {
              reference: `Patient/${patientId}`,
              display: `${formValues.firstName} ${formValues.lastName}`,
            },
            participant: [
              {
                member: {
                  reference: practitionerId,
                  display: `${formValues.firstName} ${formValues.lastName}`,
                },
              },
              ...previousPractioner.filter(Boolean),
            ],
          },
          request: { method: 'PUT', url: `CareTeam/${CareTeamID}` },
        },
      ],
    };
    const response = await axios.post(`${MEDPLUM_API_URL}/fhir/R4`, myRequest, {
      headers: AuthService.instance.withAuthHeader(),
    });
    if (formValues?.is_primary) {
      await careTeam.addCareAsPrimary(
        patientId,
        response.data?.entry?.find((item: any) => item.resource?.resourceType === 'Practitioner')?.resource?.id,
        `${formValues.firstName} ${formValues.lastName}`
      );
    }
    if (!(response?.status === 201 || response?.status === 200)) {
      throw new Error('Failed to create Care Team.');
    }
    return { success: response?.status === 201 || response?.status === 200 };
  },
  async editNonFluent(patientId: Patient['id'], formValue: any) {
    const { formValues, practitionerId, practitionerRoleId } = formValue;

    const practitionerResource = {
      resourceType: 'Practitioner',
      id: practitionerId,
      name: [{ given: [formValues.firstName], family: formValues.lastName }],
      telecom: [
        { system: 'email', use: 'work', value: formValues.email },
        { system: 'phone', use: 'mobile', value: formValues.phoneNumber },
        { system: 'phone', use: 'other', value: formValues.altPhoneNumber },
      ],
      address: [{ city: formValues.city, country: formValues.country }],
      meta: {
        account: { reference: `Patient/${patientId}` },
        tag: [],
      },
    };

    const practitionerResponse = await axios.put(
      `${MEDPLUM_API_URL}/fhir/R4/Practitioner/${practitionerId}`,
      practitionerResource,
      { headers: AuthService.instance.withAuthHeader() }
    );

    if (!(practitionerResponse?.status === 200 || practitionerResponse?.status === 201)) {
      throw new Error('Failed to update Practitioner.');
    }

    if (formValues.speciality && practitionerRoleId) {
      const practitionerRolePatchPayload = [
        {
          op: 'replace',
          path: '/specialty',
          value: [
            {
              coding: [
                {
                  system: SNOMED_URL,
                  code: `sty:${formValues.speciality?.toLowerCase()}`,
                  display: formValues.speciality?.replace('sty:', '').toUpperCase(),
                },
              ],
            },
          ],
        },
      ];

      const patchResponse = await axios.patch(
        `${MEDPLUM_API_URL}/fhir/R4/PractitionerRole/${practitionerRoleId}`,
        practitionerRolePatchPayload,
        { headers: { ...AuthService.instance.withAuthHeader(), 'Content-Type': 'application/json-patch+json' } }
      );

      if (!(patchResponse?.status === 200 || patchResponse?.status === 201)) {
        throw new Error('Failed to update PractitionerRole specialty.');
      }
    }

    if (formValues?.is_primary) {
      await careTeam.addCareAsPrimary(patientId, practitionerId, `${formValues.firstName} ${formValues.lastName}`);
    }

    if (!formValues?.is_primary && formValues?.is_primary !== formValues.subMember?.is_primary) {
      await careTeam.removeCareAsPrimary(patientId);
    }

    return { success: true };
  },
};

const doctorList = {
  async getAll(name: string) {
    const { data, status } = await axios.post(
      MEDPLUM_GRAPHQL_API_URL,
      {
        query: medplumGraphQlQuery.doctorList.getAll(name),
      },
      {
        headers: AuthService.instance.withAuthHeader(),
      }
    );

    if (status !== 200) {
      throw new ApiError(data);
    }

    return data;
  },
};
const valueSet = {
  async getValueSetCareTeam(isPublicMode: boolean, accessToken?: any) {
    const { data } = await axios.post(
      MEDPLUM_GRAPHQL_API_URL,
      {
        query: medplumGraphQlQuery.valueSet.getAllCareTeam(),
      },
      {
        headers: isPublicMode
          ? AuthService.instance.withAuthHeader({ Authorization: `Bearer ${accessToken}` })
          : AuthService.instance.withAuthHeader(),
      }
    );

    return data;
  },
  async getAllQuestionnaire(url: string, isPublicMode: boolean, accessToken?: any) {
    // Use getAllValueSetFromDirectus only for FACT-Category-sup URLs
    if (isPublicMode) {
      return {};
    }
    if (url.includes('FACT-Category-sup')) {
      const valueSetType = url.replace(`${FHIR_VALUE_SET_URL}/`, '');
      const { data, status } = await axios.get(
        `${
          isPublicMode ? MEDPLUM_API_URL_REST : MED_PLUM_API_URL_REST
        }/ValueSet/$expand?url=${FHIR_VALUE_SET_URL}/${valueSetType}`,
        {
          headers: isPublicMode
            ? AuthService.instance.withAuthHeader({ Authorization: `Bearer ${accessToken}` })
            : AuthService.instance.withAuthHeader(),
        }
      );
      if (status !== 200 && status !== 201) throw new ApiError(data);
      const directusData = data?.expansion?.contains || data;
      return { [url]: [{ compose: { include: [{ concept: directusData }] } }] };
    }
    const {
      data: {
        data: { ValueSetList },
      },
    } = await axios.post(
      isPublicMode ? MEDPLUM_GRAPHQL_SHARE_API_URL : MEDPLUM_GRAPHQL_API_URL,
      {
        query: medplumGraphQlQuery.valueSet.getAllQuestionnaire(url),
      },
      {
        headers: isPublicMode
          ? AuthService.instance.withAuthHeader({ Authorization: `Bearer ${accessToken}` })
          : AuthService.instance.withAuthHeader(),
      }
    );

    return { [url]: ValueSetList };
  },
};

const valueSetList = {
  async getAllQuestionnaireList(urls: string[], isPublicMode: boolean, accessToken?: any) {
    const promiseRequests = urls?.map((url: string) => valueSet.getAllQuestionnaire(url, isPublicMode, accessToken));
    const data = await Promise.all(promiseRequests);
    return data;
  },

  async getAllValueSetHL7(isPublicMode: boolean, valueSetType: string, accessToken?: any) {
    const { data, status } = await axios.get(`${API_GATEWAY_URL}/emr/fhir/R4/ValueSet/$expand?url=${valueSetType}`, {
      headers: isPublicMode
        ? AuthService.instance.withAuthHeader({ Authorization: `Bearer ${accessToken}` })
        : AuthService.instance.withAuthHeader(),
    });
    if (status !== 200 && status !== 201) throw new ApiError(data);
    return data.expansion.contains || data;
  },
  async getAllValueSetFromTaxonomy(isPublicMode: boolean, searchText: string, valueSetType: string, accessToken?: any) {
    const { data, status } = await axios.get(
      `${API_GATEWAY_URL}/taxonomy/fhir/ValueSet/$expand?url=${SNOMED_URL}${valueSetType}${searchText || ''}`,
      {
        headers: isPublicMode
          ? AuthService.instance.withAuthHeader({ Authorization: `Bearer ${accessToken}` })
          : AuthService.instance.withAuthHeader(),
      }
    );
    if (status !== 200 && status !== 201) throw new ApiError(data);

    return data.expansion.contains;
  },
  async getAllValueSetFromDirectus(isPublicMode: boolean, valueSetType: string, accessToken?: any) {
    const baseUrl = isPublicMode ? MEDPLUM_API_URL_REST : MED_PLUM_API_URL_REST;
    const { data, status } = await axios.get(`${baseUrl}/ValueSet/$expand?url=${FHIR_VALUE_SET_URL}/${valueSetType}`, {
      headers: isPublicMode
        ? AuthService.instance.withAuthHeader({ Authorization: `Bearer ${accessToken}` })
        : AuthService.instance.withAuthHeader(),
    });
    if (status !== 200 && status !== 201) throw new ApiError(data);
    return data?.expansion?.contains || data;
  },
  async getAllValueSetFromShareDirectus(isPublicMode: boolean, valueSetType: string, accessToken?: any) {
    const { data, status } = await axios.get(
      `${MEDPLUM_API_URL_REST}/ValueSet/$expand?url=${FHIR_VALUE_SET_URL}/${valueSetType}`,
      {
        headers: isPublicMode
          ? AuthService.instance.withAuthHeader({ Authorization: `Bearer ${accessToken}` })
          : AuthService.instance.withAuthHeader(),
      }
    );
    if (status !== 200 && status !== 201) throw new ApiError(data);
    return data?.expansion?.contains || data;
  },
  async getMedicationFromSnomed(isPublicMode: boolean, accessToken?: any, searchText?: string) {
    const filter = searchText ? `&filter=${searchText}` : '';
    const url = `${MEDPLUM_API_URL}/fhir/R4/ValueSet/$expand?url=${SNOMED_MEDICATION_FHIR}&count=100${filter}`;
    const { data, status } = await axios.get(url, {
      headers: AuthService.instance.withAuthHeader(
        isPublicMode ? { Authorization: `Bearer ${accessToken}` } : undefined
      ),
    });
    if (status !== 200 && status !== 201) throw new ApiError(data);
    return data?.expansion?.contains || data;
  },
  async getFHIRCodingFromCMS(factCode: string, accessToken?: any) {
    const query = `query FactsList($factCode: String!) { fact(filter: { fact_code: { _eq: $factCode } }) { fact_code fact_display emr_code emr_display emr_system } }`;
    const { data, status } = await axios.post(
      CMS_GRAPHQL_API_URL,
      { query, variables: { factCode } },
      { headers: accessToken ? { Authorization: `Bearer ${accessToken}` } : {} }
    );
    if (![200, 201].includes(status)) throw new ApiError(data);
    if (data.errors?.length) throw new ApiError(data.errors[0].message);
    const fact = data?.data?.fact?.[0];
    return fact
      ? [
          { system: FHIR_VALUE_SET_FACT_URL, display: fact.fact_display, code: fact.fact_code },
          { system: fact.emr_system, display: fact.emr_display, code: fact.emr_code },
        ]
      : [];
  },
};

const shareProfileRecord = {
  async getSharePatientData() {
    const [searchParams] = useSearchParams();
    const tempToken = searchParams.get('access_token');
    const {
      data: { data },
      status,
    } = await axios.post(
      MEDPLUM_GRAPHQL_SHARE_API_URL,
      {
        query: medplumGraphQlQuery.shareProfileRecord.getSharePatientData(),
      },
      {
        headers: {
          Authorization: `Bearer ${tempToken}`,
          'Content-Type': enumContentType.JSON,
        },
      }
    );
    if (status !== 200) {
      throw new ApiError(data);
    }
    return data?.PatientList?.[0];
  },
  async getShareBasicInfo(patientId: Patient['id'], url: Questionnaire['url'], tempToken?: string) {
    const {
      data: { data },
      status,
    } = await axios.post(
      MEDPLUM_GRAPHQL_SHARE_API_URL,
      {
        query: medplumGraphQlQuery.shareProfileRecord.getShareBasicInfo(patientId, url),
      },
      {
        headers: {
          ...(tempToken ? { Authorization: `Bearer ${tempToken}` } : {}),
          'Content-Type': enumContentType.JSON,
        },
      }
    );
    if (status !== 200) {
      throw new ApiError(data);
    }
    return data;
  },
  async getShareProfileURL(entryId: any) {
    const {
      data: { data },
      status,
    } = await axios.post(
      MEDPLUM_GRAPHQL_API_URL,
      {
        query: medplumGraphQlQuery.shareProfileRecord.getShareProfileURL(entryId),
      },
      {
        headers: AuthService.instance.withAuthHeader(),
      }
    );
    if (status !== 200) {
      throw new ApiError(data);
    }
    return data;
  },
  async createTaskForShare(patientId: Patient['id'], payload: any) {
    const today: Date = new Date();
    const daysToExpire = parseInt(payload?.daysToExpire, 10) || 0;
    const exactDateToExpire = addDays(today, daysToExpire);

    const consentUUID = 'urn:uuid:dbfdab3c-f85e-4d44-90f7-e81b82ee5299';

    const consentResource = {
      resourceType: 'Consent',
      status: 'active',
      scope: {
        coding: [
          {
            system: 'http://terminology.hl7.org/CodeSystem/consentscope',
            code: 'patient-privacy',
            display: 'Privacy Consent',
          },
        ],
      },
      category: [
        {
          coding: [
            {
              system: 'http://loinc.org',
              code: '59284-0',
              display: 'Patient Consent',
            },
          ],
        },
      ],
      policyRule: {
        coding: [
          {
            system: 'http://terminology.hl7.org/CodeSystem/consentpolicycodes',
            code: 'cric',
            display: 'Common Rule Informed Consent',
          },
        ],
      },
      patient: { reference: `Patient/${patientId}` },
      dateTime: today,
      performer: [{ reference: `Patient/${patientId}` }],
      provision: {
        type: 'permit',
        period: {
          start: today,
          end: exactDateToExpire,
        },
        // code: 
        data: payload.data,
      },
    };

    const taskResource = {
      resourceType: 'Task',
      status: 'requested',
      intent: 'order',
      code: {
        coding: [
          {
            code: 'share-records',
            display: 'Share records',
          },
        ],
      },
      input: [
        {
          type: {
            coding: [
              {
                code: 'Consent',
                display: 'Consent',
              },
            ],
          },
          valueString: consentUUID,
        },
      ],
      requester: { reference: `Patient/${patientId}` },
    };

    const bundlePayload = {
      resourceType: 'Bundle',
      type: 'transaction',
      entry: [
        {
          fullUrl: consentUUID,
          resource: consentResource,
          request: {
            method: 'POST',
            url: 'Consent',
          },
        },
        {
          resource: taskResource,
          request: {
            method: 'POST',
            url: 'Task',
          },
        },
      ],
    };

    const response = await axios.post(`${MEDPLUM_API_URL}/fhir/R4`, bundlePayload, {
      headers: AuthService.instance.withAuthHeader(),
    });

    if (response.status !== 200) throw new ApiError(response.data);
    return response.data;
  },
  async getShareRecordURL(entryId: any) {
    const {
      data: { data },
      status,
    } = await axios.post(
      MEDPLUM_GRAPHQL_API_URL,
      {
        query: medplumGraphQlQuery.shareProfileRecord.getShareRecordURL(entryId),
      },
      {
        headers: AuthService.instance.withAuthHeader(),
      }
    );
    if (status !== 200) {
      throw new ApiError(data);
    }
    return data;
  },
  async getShareData(entryId: any, accessToken: any, type: string) {
    const response = await axios
      // .get(
      // `${API_GATEWAY_URL}/emr/share/R4/Composition/${entryId}`,
      // // {
      // //   query: medplumGraphQlQuery.shareProfileRecord.getShareProfileData(entryId),
      // // },
      .post(
        `${API_GATEWAY_URL}/emr/share/R4/$graphql`,
        {
          query:
            type === 'profile'
              ? medplumGraphQlQuery.shareProfileRecord.getShareProfileData(entryId)
              : // This will be need to be updated from composotiion to other call - Best pratice would be to create a fucntion and call all get resource call inside
                // : type === 'single-record'
                // ? medplumGraphQlQuery.shareProfileRecord.getShareSingleRecordData(entryId)
                medplumGraphQlQuery.shareProfileRecord.getShareRecordData(entryId),
        },
        {
          headers: AuthService.instance.withAuthHeader({ Authorization: `Bearer ${accessToken}` }),
        }
      )
      .catch((error) => {
        throw new ApiError(error);
      });

    // if (response?.status !== 200) {
    //   throw new ApiError(response?.data);
    // }
    return response?.data?.data?.Composition;
  },
  async getShareType(entryId: any, accessToken: any) {
    const response = await axios
      .get(`${MEDPLUM_API_URL}/share/R4/Composition/${entryId}`, {
        headers: AuthService.instance.withAuthHeader({ Authorization: `Bearer ${accessToken}` }),
      })
      .catch((error) => {
        throw new ApiError(error);
      });
    return response;
  },
  async getShareFamilyMemberHistoryList(patientId: Patient['id']) {
    const [searchParams] = useSearchParams();
    const tempToken = searchParams.get('access_token');
    const {
      data: { data },
      status,
    } = await axios.post(
      MEDPLUM_GRAPHQL_SHARE_API_URL,
      {
        query: medplumGraphQlQuery.familyMemberHistory.getFamilyMemberData(patientId),
      },
      {
        headers: AuthService.instance.withAuthHeader({ Authorization: `Bearer ${tempToken}` }),
      }
    );

    if (status !== 200) {
      throw new ApiError(data);
    }

    return data;
  },
};

const downloadProfile = {
  async createDownloadProfile(payload: any) {
    const { data, status } = await axios.post(`${MEDPLUM_API_URL}/fhir/R4`, payload, {
      headers: AuthService.instance.withAuthHeader(),
    });
    if (status !== 200) throw new ApiError(data);
    return data;
  },
};

export const downloadEntireData = {
  async createDownloadData(payload: any) {
    const { data, status } = await axios.post(`${MEDPLUM_API_URL}/fhir/R4`, payload, {
      headers: AuthService.instance.withAuthHeader(),
    });
    if (status !== 200) throw new ApiError(data);
    return { status, data };
  },
};

export const deleteProfile = {
  async deleteProfileData(payload: any) {
    const { data, status } = await axios.post(`${MEDPLUM_API_URL}/fhir/R4`, payload, {
      headers: AuthService.instance.withAuthHeader(),
    });
    if (status !== 200) throw new ApiError(data);
    return { status, data };
  },

  async getTaskCountByIdentifier(patientId: string) {
    const type = 'urn:fh-workflow:task:delete:user-account';
    const {
      data: { data },
      status,
    } = await axios.post(
      MEDPLUM_GRAPHQL_API_URL,
      {
        query: medplumGraphQlQuery.deleteProfile.getTaskCountByIdentifier(patientId, type),
      },
      {
        headers: AuthService.instance.withAuthHeader(),
      }
    );
    if (status !== 200) {
      throw new ApiError(data);
    }
    return data;
  },
};

const uploadFileHandler = async (
  file: File,
  onUploadProgress?: (progressEvent: AxiosProgressEvent, file: File) => void,
  signal?: AbortSignal // 👈 Accept signal for request cancellation
): Promise<any> => {
  try {
    const res = await axios.post(`${MEDPLUM_API_URL}/fhir/R4/Binary`, file, {
      headers: {
        ...AuthService.instance.withAuthHeader(),
        'Content-Type': file.type,
      },
      onUploadProgress: (event) => onUploadProgress && onUploadProgress(event, file),
      signal, // 👈 Pass the signal to axios to allow request cancellation
    });

    return res;
  } catch (error: any) {
    if (axios.isCancel(error)) {
      console.error(`Upload cancelled: ${file.name}`);
    } else {
      console.error({ error }, 'error');
    }
    return null;
  }
};

const auditEventList = {
  async getAll(patientId: string) {
    const type = 'http://dicom.nema.org/resources/ontology/DCM|110110';
    const {
      data: { data },
      status,
    } = await axios.post(
      MEDPLUM_GRAPHQL_API_URL,
      { query: medplumGraphQlQuery.auditEventList.getAll(patientId, type) },
      { headers: AuthService.instance.withAuthHeader() }
    );

    if (status !== 200) {
      throw new ApiError(data);
    }
    return data.AuditEventList;
  },
};

const symptomsObservation = {
  async getSymptomList(
    patientId: Patient['id'],
    identifier: string,
    isPublicMode?: boolean,
    accessToken?: string
  ): Promise<any> {
    try {
      const {
        data: { data },
        status,
      } = await axios.post(
        isPublicMode ? MEDPLUM_GRAPHQL_SHARE_API_URL : MEDPLUM_GRAPHQL_API_URL,
        {
          query: medplumGraphQlQuery.getSymptomsObservationList.getSymptomsObservation(patientId, identifier),
        },
        {
          headers:
            isPublicMode && accessToken && accessToken.trim().length > 0
              ? AuthService.instance.withAuthHeader({ Authorization: `Bearer ${accessToken}` })
              : AuthService.instance.withAuthHeader(),
        }
      );
      identifyHealthProfileUser(IdentifyProviderNames.SymptomsFlow, data?.ObservationList);

      if (status !== 200) {
        throw new ApiError(data);
      }
      return data || {};
    } catch (error) {
      throw new Error('Failed to fetch the symptom list');
    }
  },

  async addSymptoms(symptomObservationPayload: unknown): Promise<Observation> {
    try {
      const { data, status } = await axios.post(`${MED_PLUM_API_URL_REST}/Observation`, symptomObservationPayload, {
        headers: AuthService.instance.withAuthHeader(),
      });

      if ((status !== 200 && status !== 201) || data.error) {
        throw new ApiError(data);
      }

      return data?.data || {};
    } catch (error) {
      throw new Error('Something went wrong while adding the symptom');
    }
  },

  async updateSymptoms(payload: { symptomId: string; payloadSymptom: any }): Promise<Observation> {
    try {
      const { symptomId, payloadSymptom } = payload;
      const { data, status } = await axios.put(`${MED_PLUM_API_URL_REST}/Observation/${symptomId}`, payloadSymptom, {
        headers: AuthService.instance.withAuthHeader(),
      });

      if (status !== 200 || data.error) {
        throw new ApiError(data);
      }

      return data?.data || {};
    } catch (error) {
      throw new Error('Something went wrong while updating the symptom');
    }
  },
  async deleteSymptom(symptomId: any) {
    const identifier = 'urn:fh-workflow:task:delete:symptom';
    const symptomResponseUpdate = Buffer.from(
      JSON.stringify([
        {
          op: 'add',
          path: '/meta/tag',
          value: [
            {
              system: FH_UI_CODESYSTEM,
              code: 'delete',
              display: 'Marked for deletion',
            },
          ],
        },
      ])
    ).toString('base64');
    const payload = {
      resourceType: 'Bundle',
      type: 'transaction',
      entry: [
        {
          fullUrl: DELETE_QUESTIONNAIRE_TASK,
          resource: {
            resourceType: 'Task',
            status: 'requested',
            intent: 'option',
            priority: 'routine',
            identifier: [
              {
                value: identifier,
              },
            ],
            input: [
              {
                type: {
                  coding: [
                    {
                      code: identifier,
                    },
                  ],
                },
                valueReference: {
                  reference: `Observation/${symptomId}`,
                },
              },
            ],
          },
          request: {
            method: 'POST',
            url: 'Task',
          },
        },
        {
          resource: {
            resourceType: 'Binary',
            contentType: 'application/json-patch+json',
            data: `{{${symptomResponseUpdate}}}`,
          },
          request: {
            method: 'PATCH',
            url: `Observation/${symptomId}`,
          },
        },
      ],
    };
    const response = await axios.post(`${MEDPLUM_API_URL}/fhir/R4`, payload, {
      headers: AuthService.instance.withAuthHeader(),
    });

    if (response?.status !== 200) {
      throw new Error('Failed to delete symptom.');
    }
    const success = response?.status === 200;
    return { success };
  },
};

const procedureList = {
  async getProcedureList(patientId: Patient['id'], accessToken?: string) {
    const { isPublicMode } = usePublicSettings();
    const {
      data: { data },
      status,
    } = await axios.post(
      isPublicMode ? MEDPLUM_GRAPHQL_SHARE_API_URL : MEDPLUM_GRAPHQL_API_URL,
      {
        query: medplumGraphQlQuery.getProcedureList.getAll(patientId),
      },
      {
        headers:
          isPublicMode && accessToken && accessToken.trim() !== ''
            ? AuthService.instance.withAuthHeader({ Authorization: `Bearer ${accessToken}` })
            : AuthService.instance.withAuthHeader(),
      }
    );
    identifyHealthProfileUser(IdentifyProviderNames.Surgeries, data?.ProcedureList);
    if (status !== 200) {
      throw new ApiError(data);
    }
    return data;
  },

  async addSurgeryProcedure(payload: any) {
    try {
      const response = await axios.post(`${MED_PLUM_API_URL_REST}/Procedure`, payload, {
        headers: AuthService.instance.withAuthHeader(),
      });

      const { data, status: responseStatus } = response;

      if ((responseStatus !== 200 && responseStatus !== 201) || data.error) {
        throw new ApiError(data);
      }

      return data?.data || {};
    } catch (error) {
      throw new Error('Something went wrong while adding the surgery or procedure');
    }
  },

  async updateSurgeryProcedure(payload: { procedureId: string; procedurePayload: any }) {
    try {
      const { procedureId, procedurePayload } = payload;
      const response = await axios.put(`${MED_PLUM_API_URL_REST}/Procedure/${procedureId}`, procedurePayload, {
        headers: AuthService.instance.withAuthHeader(),
      });

      const { data, status: responseStatus } = response;

      if (responseStatus !== 200 || data.error) {
        throw new ApiError(data);
      }

      return data?.data || {};
    } catch (error) {
      throw new Error('Something went wrong while updating the surgery or procedure');
    }
  },

  async deleteSurgeryProcedureTask(payload: any) {
    const { surgeryProcedureId, identifier } = payload || {};

    const surgeryProcedureUpdate = Buffer.from(
      JSON.stringify([
        {
          op: 'add',
          path: '/meta/tag',
          value: [
            {
              system: FH_UI_CODESYSTEM,
              code: 'delete',
              display: 'Marked for deletion',
            },
          ],
        },
      ])
    ).toString('base64');

    const batchRequest = {
      resourceType: 'Bundle',
      type: 'transaction',
      entry: [
        {
          fullUrl: DELETE_QUESTIONNAIRE_TASK,
          resource: {
            resourceType: 'Task',
            status: 'requested',
            intent: 'option',
            priority: 'routine',
            identifier: [
              {
                value: identifier,
              },
            ],
            input: [
              {
                type: {
                  coding: [
                    {
                      code: identifier,
                    },
                  ],
                },
                valueReference: {
                  reference: `Procedure/${surgeryProcedureId}`,
                },
              },
            ],
          },
          request: {
            method: 'POST',
            url: 'Task',
          },
        },
        {
          resource: {
            resourceType: 'Binary',
            contentType: 'application/json-patch+json',
            data: `{{${surgeryProcedureUpdate}}}`,
          },
          request: {
            method: 'DELETE',
            url: `Procedure/${surgeryProcedureId}`,
          },
        },
      ],
    };
    const response = await axios.post(`${MED_PLUM_API_URL_REST}`, batchRequest, {
      headers: AuthService.instance.withAuthHeader(),
    });

    if (response?.status !== 200) {
      throw new Error('Failed to delete surgery / procedure.');
    }
    const success = response?.status === 200;
    return { success };
  },
};

const icd10System = {
  async getICD10SystemValue(code: string) {
    try {
      const response = await axios.get(
        `${API_GATEWAY_URL}/taxonomy/fhir/ConceptMap/$translate?code=${code}&system=${SNOMED_URL}`,
        {
          headers: AuthService.instance.withAuthHeader(),
        }
      );

      return response.data || {};
    } catch (error) {
      throw new Error('Something went wrong while adding the surgery or procedure');
    }
  },
};

export const valueSetsForProfile = {
  async getValueSetsForProfile(urls: { url: string; type: string }[]) {
    const {
      data: { data },
      status,
    } = await axios.post(
      MEDPLUM_GRAPHQL_API_URL,
      {
        query: medplumGraphQlQuery.valueSetsForProfile.getValuesetsForProfile(urls),
      },
      {
        headers: AuthService.instance.withAuthHeader(),
      }
    );

    if (status !== 200) {
      throw new ApiError(data);
    }

    return data;
  },
};

const allergyIntolerance = {
  async getAllergyIntoleranceList(patientId: Patient['id'], isPublicMode: boolean, accessToken?: any): Promise<any> {
    try {
      const {
        data: { data },
        status,
      } = await axios.post(
        isPublicMode ? MEDPLUM_GRAPHQL_SHARE_API_URL : MEDPLUM_GRAPHQL_API_URL,
        {
          query: medplumGraphQlQuery.getAllergiesIntolerancesList.getAllergiesIntolerances(patientId),
        },
        {
          headers: isPublicMode
            ? AuthService.instance.withAuthHeader({ Authorization: `Bearer ${accessToken}` })
            : AuthService.instance.withAuthHeader(),
        }
      );
      identifyHealthProfileUser(IdentifyProviderNames.Allergies, data?.AllergyIntoleranceList);

      if (status !== 200) {
        throw new ApiError(data);
      }
      return data || {};
    } catch (error) {
      throw new Error('Failed to fetch the allergyIntolerance list');
    }
  },

  async addAllergiesIntolerances(allergyIntoleranceAllergyIntolerancePayload: unknown): Promise<AllergyIntolerance> {
    try {
      const { data, status } = await axios.post(
        `${MED_PLUM_API_URL_REST}/AllergyIntolerance`,
        allergyIntoleranceAllergyIntolerancePayload,
        {
          headers: AuthService.instance.withAuthHeader(),
        }
      );

      if ((status !== 200 && status !== 201) || data.error) {
        throw new ApiError(data);
      }

      return data?.data || {};
    } catch (error) {
      throw new Error('Something went wrong while adding the allergyIntolerance');
    }
  },

  async updateAllergiesIntolerances(payload: {
    allergyId: string;
    payloadAllergyIntolerance: any;
  }): Promise<AllergyIntolerance> {
    try {
      const { allergyId, payloadAllergyIntolerance } = payload;
      const { data, status } = await axios.put(
        `${MED_PLUM_API_URL_REST}/AllergyIntolerance/${allergyId}`,
        payloadAllergyIntolerance,
        {
          headers: AuthService.instance.withAuthHeader(),
        }
      );

      if (status !== 200 || data.error) {
        throw new ApiError(data);
      }

      return data?.data || {};
    } catch (error) {
      throw new Error('Something went wrong while updating the allergyIntolerance');
    }
  },
  async deleteAllergyIntolerance(allergyId: any) {
    const identifier = 'urn:fh-workflow:task:delete:family-history';
    const allergyIntoleranceResponseUpdate = Buffer.from(
      JSON.stringify([
        {
          op: 'add',
          path: '/meta/tag',
          value: [
            {
              system: FH_UI_CODESYSTEM,
              code: 'delete',
              display: 'Marked for deletion',
            },
          ],
        },
      ])
    ).toString('base64');
    const payload = {
      resourceType: 'Bundle',
      type: 'transaction',
      entry: [
        {
          fullUrl: DELETE_QUESTIONNAIRE_TASK,
          resource: {
            resourceType: 'Task',
            status: 'requested',
            intent: 'option',
            priority: 'routine',
            identifier: [
              {
                value: identifier,
              },
            ],
            input: [
              {
                type: {
                  coding: [
                    {
                      code: identifier,
                    },
                  ],
                },
                valueReference: {
                  reference: `AllergyIntolerance/${allergyId}`,
                },
              },
            ],
          },
          request: {
            method: 'POST',
            url: 'Task',
          },
        },
        {
          resource: {
            resourceType: 'Binary',
            contentType: 'application/json-patch+json',
            data: `{{${allergyIntoleranceResponseUpdate}}}`,
          },
          request: {
            method: 'PATCH',
            url: `AllergyIntolerance/${allergyId}`,
          },
        },
      ],
    };
    const response = await axios.post(`${MEDPLUM_API_URL}/fhir/R4`, payload, {
      headers: AuthService.instance.withAuthHeader(),
    });

    if (response?.status !== 200) {
      throw new Error('Failed to delete allergyIntolerance.');
    }
    const success = response?.status === 200;
    return { success };
  },
};

const preventativeScreening = {
  async getPreventativeScreeningPatient(
    patientId: string,
    identifier: string[],
    count?: number,
    isPublicMode?: boolean,
    accessToken?: string
  ) {
    const {
      data: { data },
      status,
    } = await axios.post(
      isPublicMode ? MEDPLUM_GRAPHQL_SHARE_API_URL : MEDPLUM_GRAPHQL_API_URL,
      {
        query: medplumGraphQlQuery.getPreventativeScreeningList.getPreventativeScreening(patientId, identifier),
        variables: {
          count,
        },
      },
      {
        headers:
          isPublicMode && accessToken && accessToken !== 'undefined'
            ? AuthService.instance.withAuthHeader({ Authorization: `Bearer ${accessToken}` })
            : AuthService.instance.withAuthHeader(),
      }
    );

    if (status !== 200 || data.error) {
      throw new ApiError(data);
    }
    identifyHealthProfileUser(IdentifyProviderNames.PreventativeScreening, data);
    return data;
  },

  async addPreventativeScreening(preventativeScreenings: any): Promise<Procedure> {
    try {
      const response = await axios.post(`${MED_PLUM_API_URL_REST}/Procedure`, preventativeScreenings, {
        headers: AuthService.instance.withAuthHeader(),
      });

      const { data, status: responseStatus } = response;

      if ((responseStatus !== 200 && responseStatus !== 201) || data.error) {
        throw new ApiError(data);
      }

      return data?.data || {};
    } catch (error) {
      throw new Error('Something went wrong while adding the condition');
    }
  },

  async updatePreventativeScreen(preventativeScreeningId: string, payload: any): Promise<Observation> {
    try {
      const response = await axios.patch(`${MED_PLUM_API_URL_REST}/Procedure/${preventativeScreeningId}`, payload, {
        headers: AuthService.instance.withAuthHeader(),
      });

      const { data, status: responseStatus } = response;

      if (responseStatus !== 200 || data.error) {
        throw new ApiError(data);
      }

      return data?.data || {};
    } catch (error) {
      throw new Error('Something went wrong while updating the condition');
    }
  },
  async deletePreventativeScreening(screeningId: string) {
    const identifier = 'urn:fh-workflow:task:delete:screening';
    const screeningResponseUpdate = Buffer.from(
      JSON.stringify([
        {
          op: 'add',
          path: '/meta/tag',
          value: [
            {
              system: FH_UI_CODESYSTEM,
              code: 'delete',
              display: 'Marked for deletion',
            },
          ],
        },
      ])
    ).toString('base64');

    const payload = {
      resourceType: 'Bundle',
      type: 'transaction',
      entry: [
        {
          fullUrl: DELETE_QUESTIONNAIRE_TASK,
          resource: {
            resourceType: 'Task',
            status: 'requested',
            intent: 'option',
            priority: 'routine',
            identifier: [
              {
                value: identifier,
              },
            ],
            input: [
              {
                type: {
                  coding: [
                    {
                      code: identifier,
                    },
                  ],
                },
                valueReference: {
                  reference: `Procedure/${screeningId}`,
                },
              },
            ],
          },
          request: {
            method: 'POST',
            url: 'Task',
          },
        },
        {
          resource: {
            resourceType: 'Binary',
            contentType: 'application/json-patch+json',
            data: `{{${screeningResponseUpdate}}}`,
          },
          request: {
            method: 'PATCH',
            url: `Procedure/${screeningId}`,
          },
        },
      ],
    };

    const response = await axios.post(`${MEDPLUM_API_URL}/fhir/R4`, payload, {
      headers: AuthService.instance.withAuthHeader(),
    });

    if (response?.status !== 200) {
      throw new Error('Failed to delete preventative screening.');
    }
    return { success: response?.status === 200 };
  },
};
const immunization = {
  async getImmunizationList(patientId: Patient['id'], isPublicMode?: boolean, accessToken?: string) {
    const {
      data: { data },
      status,
    } = await axios.post(
      isPublicMode ? MEDPLUM_GRAPHQL_SHARE_API_URL : MEDPLUM_GRAPHQL_API_URL,
      {
        query: medplumGraphQlQuery.getImmunizationList.getAll(patientId),
      },
      {
        headers:
          isPublicMode && accessToken && accessToken !== 'undefined'
            ? AuthService.instance.withAuthHeader({ Authorization: `Bearer ${accessToken}` })
            : AuthService.instance.withAuthHeader(),
      }
    );
    if (status !== 200) {
      throw new ApiError(data);
    }
    identifyHealthProfileUser(IdentifyProviderNames.Vaccines, data?.ImmunizationList);
    return data;
  },

  async addImmunization(payload: any) {
    try {
      const response = await axios.post(`${MED_PLUM_API_URL_REST}/Immunization`, payload.immunization, {
        headers: AuthService.instance.withAuthHeader(),
      });

      if (response.status !== 200 && response.status !== 201) {
        throw new ApiError(response.data);
      }

      return response.data || {};
    } catch (error) {
      console.error('Error adding immunization:', error);
      throw new Error('Something went wrong while adding the immunization record');
    }
  },

  async updateImmunization(patientId: Patient['id'], payload: any) {
    try {
      const { immunizationId, immunizationPayload } = payload;
      const response = await axios.put(`${MED_PLUM_API_URL_REST}/Immunization/${immunizationId}`, immunizationPayload, {
        headers: AuthService.instance.withAuthHeader(),
      });
      const { data, status } = response;
      if (status !== 200 || data.error) {
        throw new ApiError(data);
      }
      return data?.data || {};
    } catch (error) {
      throw new Error('Something went wrong while updating the immunization record');
    }
  },

  async deleteImmunizationTask(payload: any) {
    const { immunizationId, identifier } = payload || {};
    const immunizationUpdate = Buffer.from(
      JSON.stringify([
        {
          op: 'add',
          path: '/meta/tag',
          value: [
            {
              system: FH_UI_CODESYSTEM,
              code: 'delete',
              display: 'Marked for deletion',
            },
          ],
        },
      ])
    ).toString('base64');

    const batchRequest = {
      resourceType: 'Bundle',
      type: 'transaction',
      entry: [
        {
          fullUrl: DELETE_IMMUNIZATION_TASK,
          resource: {
            resourceType: 'Task',
            status: 'requested',
            intent: 'option',
            priority: 'routine',
            identifier: [
              {
                value: identifier,
              },
            ],
            input: [
              {
                type: {
                  coding: [
                    {
                      code: identifier,
                    },
                  ],
                },
                valueReference: {
                  reference: `Immunization/${immunizationId}`,
                },
              },
            ],
          },
          request: {
            method: 'POST',
            url: 'Task',
          },
        },
        {
          resource: {
            resourceType: 'Binary',
            contentType: 'application/json-patch+json',
            data: `{{${immunizationUpdate}}}`,
          },
          request: {
            method: 'DELETE',
            url: `Immunization/${immunizationId}`,
          },
        },
      ],
    };
    const response = await axios.post(`${MED_PLUM_API_URL_REST}`, batchRequest, {
      headers: AuthService.instance.withAuthHeader(),
    });
    if (response?.status !== 200) {
      throw new Error('Failed to delete immunization record.');
    }
    return { success: response?.status === 200 };
  },
};
const medication = {
  async getMedicationList(patientId: Patient['id'], isPublicMode?: boolean, accessToken?: string): Promise<any> {
    try {
      const {
        data: { data },
        status,
      } = await axios.post(
        isPublicMode ? MEDPLUM_GRAPHQL_SHARE_API_URL : MEDPLUM_GRAPHQL_API_URL,
        {
          query: medplumGraphQlQuery.getMedicationsList.getAll(patientId),
        },
        {
          headers:
            isPublicMode && accessToken && accessToken !== 'undefined'
              ? AuthService.instance.withAuthHeader({ Authorization: `Bearer ${accessToken}` })
              : AuthService.instance.withAuthHeader(),
        }
      );

      if (status !== 200) {
        throw new ApiError(data);
      }
      identifyHealthProfileUser(IdentifyProviderNames.Medications, data?.MedicationStatementList);
      identifyHealthProfileUser(IdentifyProviderNames.Supplements, data?.MedicationStatementList);
      return data?.MedicationStatementList || {};
    } catch (error) {
      throw new Error('Failed to fetch the medication list');
    }
  },

  async addMedication(patientid: Patient['id'], medicationPayload: unknown): Promise<any> {
    try {
      const { data, status } = await axios.post(`${MED_PLUM_API_URL_REST}/MedicationStatement`, medicationPayload, {
        headers: AuthService.instance.withAuthHeader(),
      });

      if ((status !== 200 && status !== 201) || data.error) {
        throw new ApiError(data);
      }
      return data?.data || {};
    } catch (error) {
      throw new Error('Something went wrong while adding the medication');
    }
  },

  async updateMedication(payload: any): Promise<any> {
    try {
      const { payloadMedication } = payload;
      const { data, status } = await axios.put(
        `${MED_PLUM_API_URL_REST}/MedicationStatement/${payloadMedication?.id}`,
        payloadMedication,
        {
          headers: AuthService.instance.withAuthHeader(),
        }
      );

      if (status !== 200 || data.error) {
        throw new ApiError(data);
      }
      return data?.data || {};
    } catch (error) {
      throw new Error('Something went wrong while updating the medication');
    }
  },

  async deleteMedication(medicationId: any, identifier: string) {
    try {
      const medicationUpdate = Buffer.from(
        JSON.stringify([
          {
            op: 'add',
            path: '/meta/tag',
            value: [{ system: FH_UI_CODESYSTEM, code: 'delete', display: 'Marked for deletion' }],
          },
        ])
      ).toString('base64');

      // Create a Bundle transaction payload
      const batchRequest = {
        resourceType: 'Bundle',
        type: 'transaction',
        entry: [
          {
            fullUrl: DELETE_QUESTIONNAIRE_TASK,
            resource: {
              resourceType: 'Task',
              status: 'requested',
              intent: 'option',
              priority: 'routine',
              identifier: [
                {
                  value: identifier,
                },
              ],
              input: [
                {
                  type: {
                    coding: [
                      {
                        code: identifier,
                      },
                    ],
                  },
                  valueReference: {
                    reference: `MedicationStatement/${medicationId}`,
                  },
                },
              ],
            },
            request: {
              method: 'POST',
              url: 'Task',
            },
          },
          {
            resource: {
              resourceType: 'Binary',
              contentType: 'application/json-patch+json',
              data: `{{${medicationUpdate}}}`,
            },
            request: {
              method: 'DELETE',
              url: `MedicationStatement/${medicationId}`,
            },
          },
        ],
      };

      const response = await axios.post(`${MED_PLUM_API_URL_REST}`, batchRequest, {
        headers: AuthService.instance.withAuthHeader(),
      });

      if (response?.status !== 200) {
        throw new Error('Failed to delete medication record.');
      }
      return { success: response?.status === 200 };
    } catch (error) {
      throw new Error('Something went wrong while deleting the medication');
    }
  },
};
const communicationRequest = {
  async addCommunicationRequest(patientId: string, payload: { template: string; reason1: string }) {
    try {
      const content = Object.entries(payload)
        .filter(([key]) => key !== 'basedOn' && key !== 'about')
        .reduce((acc, [key, value]) => {
          acc[key] = value;
          return acc;
        }, {} as Record<string, string>);

      const requestPayload = {
        resourceType: 'CommunicationRequest',
        status: 'draft',
        subject: {
          reference: `Patient/${patientId}`,
        },
        payload: [
          {
            contentString: JSON.stringify(content),
          },
        ],
        category: [
          {
            coding: [
              {
                system: FHIR_HL7_CODE_SYSTEM_COMMUNICATION_CATEGORY,
                code: 'notification',
                display: 'Notification',
              },
            ],
          },
        ],
        medium: [
          {
            coding: [
              {
                system: FH_CODE_SYSTEM_FLUENT_HEALTH_UI,
                code: 'Communication-EMAIL',
                display: 'Email Notification',
              },
            ],
          },
        ],
      };

      const { data, status } = await axios.post(`${MED_PLUM_API_URL_REST}/CommunicationRequest`, requestPayload, {
        headers: AuthService.instance.withAuthHeader(),
      });

      if ((status !== 200 && status !== 201) || data.error) {
        throw new ApiError(data);
      }

      return data;
    } catch (error) {
      throw new Error('Something went wrong while sending the email communication request');
    }
  },
};
const shareTimeline = async (patientId: string, count?: number, isPublicMode?: boolean, accessToken?: string) => {
  const { data } = await axios.post(
    MEDPLUM_GRAPHQL_SHARE_API_URL,
    {
      query: medplumGraphQlQuery.shareTimeline.sharePatientData(),
      variables: { patientId, count },
    },
    {
      headers:
        isPublicMode && accessToken && accessToken.trim() !== ''
          ? AuthService.instance.withAuthHeader({ Authorization: `Bearer ${accessToken}` })
          : AuthService.instance.withAuthHeader(),
    }
  );

  return data;
};
const shareSnapshot = async (patientId: Patient['id'], isPublicMode?: boolean, accessToken?: string) => {
  const { data } = await axios.post(
    MEDPLUM_GRAPHQL_SHARE_API_URL,
    {
      query: medplumGraphQlQuery.shareSnapshot.shareSnapshotData(patientId),
      variables: { patientId },
    },
    {
      headers:
        isPublicMode && accessToken && accessToken.trim() !== ''
          ? AuthService.instance.withAuthHeader({ Authorization: `Bearer ${accessToken}` })
          : AuthService.instance.withAuthHeader(),
    }
  );

  return data;
};
export const medplumApi = {
  patientInfo,
  emergencyContact,
  healthcareProxy,
  procedureList,
  healthInsurance,
  masterQuestionnaire,
  vaccineQuestionnaire,
  careTeam,
  doctorList,
  valueSet,
  valueSetList,
  shareProfileRecord,
  downloadProfile,
  uploadFileHandler,
  familyMemberHistory,
  auditEventList,
  downloadEntireData,
  deleteProfile,
  symptomsObservation,
  icd10System,
  valueSetsForProfile,
  allergyIntolerance,
  preventativeScreening,
  immunization,
  medication,
  communicationRequest,
  shareTimeline: {
    getShareTimelineData: shareTimeline,
  },
  shareSnapshot,
};
