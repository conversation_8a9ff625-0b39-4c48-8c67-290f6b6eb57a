import React, { PropsWithChildren, Suspense, useEffect, useState } from 'react';
import { Container, Flex, Stack, Text, useDisclosure, useToast } from '@chakra-ui/react';
import { Edit3, Trash as TrashIcon } from 'react-feather';
import { recordSymptomsEvents } from '@user/lib/events-analytics-manager';
import { medplumApi } from '@user/lib/medplum-api';
import { useNavigate } from 'react-router-dom';
import { NavigationHelper, PATIENT_DEFINED, ROUTE_ACTIONS, ROUTE_VARIABLES } from '@user/lib/constants';
import { formatDateForDisplay } from '@utils/utils';

import {
  Card,
  CardHeading,
  CardPerformedLabel,
  SidebarAddButton,
  SidebarCloseButton,
  SidebarHeading,
  SidebarHelperTooltip,
} from './SidebarComponents';
import {
  MoreActionsMenu,
  MoreActionsMenuButton,
  MoreActionsMenuItem,
  MoreActionsMenuList,
} from '../../../../components/ui/Menu';
import { ISidebarProps } from '@lib/models/misc';
import { MODAL_VARIANTS, Modal } from '../../../../components/Modal';
import { useAnalyticsService, useAuthService, usePublicSettings } from '@lib/state';
import { FACT_CODE_SYSTEM, deleteIdentifier } from '@lib/constants';
import { FormSkeleton } from '../../../../components/ui/Form';
import { SidebarEmptyState } from './SidebarEmptyState';
import ProfileSidebarSymptomsForm from './ProfileSidebarSymptomsForm';
import { LinkedDocumentsCard, LinkedDocumentsLabel } from './LinkedDocumentsCard';
import { useSymptomsList } from '../../lib/medplum-state';
import { QuestionnaireResponse } from 'src/gql/graphql';
import {
  ConsentModal,
  ConsentModalContent,
  ConsentModalFooter,
  ConsentModalHeading,
  ConsentModalPrimaryButton,
  ConsentModalSecondaryButton,
} from './ConsentModal';
import { useExtractDocumentResource } from 'src/app/medical-records/lib/state';
import { FH_STRUCTURE_DEFINITION_CLINICALSTATUS } from 'src/constants/medplumConstants';

function SymptomCard({
  title,
  startDate,
  endDate,
  onEdit,
  onRemove,
  children,
  isPublicMode = false,
  isCustomEntry,
  symptom,
}: PropsWithChildren<{
  title: string;
  startDate: string;
  endDate: string;
  onEdit: () => void;
  onRemove?: () => void;
  isPublicMode?: boolean;
  isCustomEntry?: boolean;
  symptom?: any;
}>) {
  const deleteModal = useDisclosure();
  const [loader, setLoader] = useState(false);
  const getClinicalStatus = (c: any) =>
    c.extension?.find((ext: any) => ext.url === FH_STRUCTURE_DEFINITION_CLINICALSTATUS)?.valueCodeableConcept
      ?.coding?.[0]?.code;
  return (
    <>
      <ConsentModal {...deleteModal}>
        <ConsentModalHeading>
          Are you sure you want
          <br />
          to remove this entry?
        </ConsentModalHeading>
        <ConsentModalContent>This cannot be undone.</ConsentModalContent>
        <ConsentModalFooter>
          <ConsentModalSecondaryButton
            variant="quietDanger"
            color="red.100"
            onClick={() => {
              onRemove?.();
              setLoader(true);
            }}
            isLoading={loader}
          >
            Remove
          </ConsentModalSecondaryButton>
          <ConsentModalPrimaryButton onClick={deleteModal.onClose}>Cancel</ConsentModalPrimaryButton>
        </ConsentModalFooter>
      </ConsentModal>
      <Card mt="4">
        <Flex
          gap="1"
          direction="column"
        >
          <Flex
            justifyContent="space-between"
            color="fluentHealthText.100"
          >
            <CardHeading maxWidth="90%">
              {title} {isCustomEntry && '[Custom entry]'}
            </CardHeading>
            {!isPublicMode && (
              <MoreActionsMenu>
                <MoreActionsMenuButton />
                <MoreActionsMenuList>
                  <MoreActionsMenuItem
                    icon={<Edit3 size={16} />}
                    onClick={onEdit}
                  >
                    Edit
                  </MoreActionsMenuItem>
                  <MoreActionsMenuItem
                    icon={<TrashIcon size={16} />}
                    onClick={() => deleteModal.onOpen()}
                  >
                    Delete
                  </MoreActionsMenuItem>
                </MoreActionsMenuList>
              </MoreActionsMenu>
            )}
          </Flex>
          {startDate && (
            <CardPerformedLabel>
              {startDate && formatDateForDisplay(startDate)}
              {startDate &&
                (getClinicalStatus(symptom) === 'inactive'
                  ? endDate
                    ? ` - ${formatDateForDisplay(endDate)}`
                    : ' - Unknown'
                  : ' - Present')}
            </CardPerformedLabel>
          )}
          {children}
        </Flex>
      </Card>
    </>
  );
}

export default function ProfileSidebarSymptoms({ onClose, action }: ISidebarProps) {
  const toast = useToast();
  const navigate = useNavigate();
  const [selectedSymptom, setSelectedSymptom] = React.useState<QuestionnaireResponse | null>(null);
  const { PROFILE, EHR, SYMPTOMS } = ROUTE_VARIABLES;
  const { ADD } = ROUTE_ACTIONS;

  const symptomModal = useDisclosure();
  const deleteModal = useDisclosure();
  const { authenticatedUser } = useAuthService();
  const { isPublicMode } = usePublicSettings();
  const sharePatientId = localStorage.getItem('sharePatientId');

  const { symptomListOfData, deleteSymptom } = useSymptomsList(
    `Patient/${isPublicMode ? sharePatientId : authenticatedUser?.id}`
  );

  const [symptomList, setSymptomList]: any = useState<any[]>([]);
  const [submitting, setSubmitting]: any = useState<boolean>();

  const { trackEventInFlow } = useAnalyticsService();

  useEffect(() => {
    if (isPublicMode) return;
    medplumApi.valueSetList.getAllValueSetFromDirectus(isPublicMode, FACT_CODE_SYSTEM.SYMPTOMS).then((val) => {
      const data = val.map((e: { display: string; code: string }) => ({ label: e.display, value: e.code }));
      setSymptomList(data);
    });
  }, []);

  const onAddHandler = () => {
    setSelectedSymptom(null);
    recordSymptomsEvents(trackEventInFlow, {
      EventName: 'SymptomsAddStarted',
      sy_entry_point: 'my_health_profile',
    });
    navigate(`/${PROFILE}/${EHR}/${SYMPTOMS}/${ADD}`);
  };

  const onEditHandler = (symptom: QuestionnaireResponse) => {
    setSelectedSymptom(symptom);
    symptomModal.onOpen();
    recordSymptomsEvents(trackEventInFlow, {
      EventName: 'SymptomsInteracted',
      sy_entry_point: 'my_health_profile',
    });
  };

  const onRemoveHandler = async (symptom: QuestionnaireResponse) => {
    const deleteTask = 'Symptom';
    const identifier = `${deleteIdentifier}:symptom`;
    const payload: any = {
      symptomId: symptom.id,
      deleteTask,
      identifier,
    };
    try {
      setSelectedSymptom(symptom);
      await deleteSymptom(payload);
      recordSymptomsEvents(trackEventInFlow, {
        EventName: 'SymptomsRemoved',
        sy_entry_point: 'my_health_profile',
      });
      toast({
        title: 'Symptom removed',
        description: 'Symptom has been removed from the list',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    } catch (e) {
      toast({
        title: 'Error',
        description: 'An error occurred while removing the symptom',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    } finally {
      deleteModal.onClose();
    }
  };

  useEffect(() => {
    if (action === ADD) {
      symptomModal.onOpen();
    }
  }, [action]);

  const closeFn = () => {
    symptomModal.onClose();
    if (action === ADD) navigate(NavigationHelper.getEhrView(false, 'symptoms'));
  };
  const isLoading = (incoming: boolean) => {
    setSubmitting(incoming);
  };

  return (
    <>
      <Modal
        variant={MODAL_VARIANTS.PERIWINKLE}
        title="Symptoms"
        showModalFooter={false}
        isCentered
        {...symptomModal}
        onClose={closeFn}
      >
        <Suspense fallback={<FormSkeleton />}>
          {submitting ? (
            <FormSkeleton />
          ) : (
            <ProfileSidebarSymptomsForm
              symptom={selectedSymptom}
              symptomList={symptomList}
              closeDialog={closeFn}
              isLoading={isLoading}
            />
          )}
        </Suspense>
      </Modal>

      <Container
        position="relative"
        height="full"
        overflowY="scroll"
        overflowX="hidden"
        className="hide-scrollbar"
      >
        <Stack
          py="4"
          height="full"
        >
          <Flex justifyContent="space-between">
            <SidebarHeading>Symptoms</SidebarHeading>
            <SidebarCloseButton onClick={onClose} />
          </Flex>
          {symptomListOfData?.ObservationList?.length === 0 && (
            <SidebarEmptyState
              actionButtonText="Add"
              title="Update symptoms"
              imageSrc="/empty-card-symptom.png"
              completeInfoText={isPublicMode ? undefined : '+10% to complete your profile'}
              {...(isPublicMode ? { hideActionButton: true } : { onClick: onAddHandler })}
              isPublicMode={isPublicMode}
            />
          )}
          {symptomListOfData?.ObservationList?.length > 0 && (
            <>
              {!isPublicMode && <SidebarAddButton onClick={onAddHandler}>Add</SidebarAddButton>}
              <Stack
                pt="0"
                gap="2"
                overflowY="scroll"
                height="full"
                className="hide-scrollbar"
              >
                {symptomListOfData?.ObservationList?.filter((answer: any) => {
                  return answer !== null && answer !== undefined; // Only return valid answers
                }).map((answer: any) => (
                  // Render your desired JSX or component
                  <SymptomCard
                    key={answer?.id}
                    title={answer.code?.coding?.[0]?.display}
                    startDate={answer?.effectivePeriod?.start}
                    endDate={answer?.effectivePeriod?.end}
                    onRemove={() => onRemoveHandler(answer)}
                    isPublicMode={isPublicMode}
                    onEdit={() => onEditHandler(answer)}
                    isCustomEntry={answer.code?.coding?.[0]?.code === `sym:${PATIENT_DEFINED}`}
                    symptom={answer}
                  >
                    <>
                      {answer.note?.[0]?.text && <Text> {answer.note[0].text}</Text>}
                      {answer.derivedFrom?.length > 0 && answer.derivedFrom?.[0]?.resource && (
                        <Flex
                          direction="column"
                          gap="2px"
                        >
                          <LinkedDocumentsLabel />
                          <LinkedDocumentsCard records={useExtractDocumentResource(answer.derivedFrom)} />
                        </Flex>
                      )}
                    </>
                  </SymptomCard>
                ))}
              </Stack>
            </>
          )}
          {!isPublicMode && (
            <SidebarHelperTooltip
              text="What is a symptom?"
              tooltipText="According to NCBI, a symptom is evidence of a disease, or diseases, that a person can feel that may indicate that they have a disease or condition."
            />
          )}
        </Stack>
      </Container>
    </>
  );
}
