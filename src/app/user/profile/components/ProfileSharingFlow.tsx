import React, { Suspense, useCallback, useEffect, useMemo, useReducer, useRef, useState } from 'react';
import {
  Alert,
  AlertDescription,
  Box,
  Button,
  ChakraProps,
  Checkbox,
  CheckboxGroup,
  Divider,
  Flex,
  ModalCloseButton,
  Stack,
  Text,
  chakra,
  useTheme,
  useToast,
} from '@chakra-ui/react';
import { AlertTriangle as AlertIcon } from 'react-feather';
import { Controller, FormProvider, useForm, useFormContext } from 'react-hook-form';
import { StylesConfig } from 'react-select';
import { useUpdateEffect } from 'usehooks-ts';
import {
  useEmergencyContactList,
  useHealthInsuranceList,
  useHealthcareProxyList,
  useMasterConditionResponseList,
  usePatient,
  useShareProfileRecord,
} from '@user/lib/medplum-state';
import { medplumApi } from '@user/lib/medplum-api';
import { useClient } from 'urql';

import { PUBLIC_SETTINGS_PROPERTY_NAME, PublicSettingsPayload } from '@lib/models/public-settings';
import { downloadFileQuery, downloadProfile, useAnalyticsService, useAuthService, usePublicSettings } from '@lib/state';
import { FluentHealthLoader } from '../../../../components/FluentHealthLoader';
import { MODAL_VARIANTS, Modal, useModal } from '../../../../components/Modal';
import { colors } from '../../../../components/theme/colors';
import { hexOpacity } from '../../../../components/theme/utils';
import { CopyToClipboardInput } from '../../../../components/ui/Input/CopyToClipboardInput';
import { SELECT_STYLES as BASE_SELECT_STYLES, Select, SelectOptionProps } from '../../../../components/ui/Select';
import { useIsMobile } from 'src/components/ui/hooks/device.hook';
import { downloadFile as downloadFileUtil, parsePatientName } from '@lib/utils/utils';
import { ProfileConditionAnalyticsProps } from '@lib/models/analytics';
import { AnalyticsEventName, AnalyticsFlow, EventPropsNames } from '@lib/analyticsService';
import { FH_CODE_SYSTEM_FLUENT_HEALTH_UI } from 'src/constants/medplumConstants';

import { ReactComponent as ShareIcon } from '@assets/icons/share.svg';
import { ReactComponent as RightCircleArrow } from '@assets/icons/right-circle-arrow.svg';
import { ReactComponent as DownloadIcon } from '@assets/icons/download.svg';

type CheckboxItem = {
  label: string;
  value: string;
  checked?: boolean;
  payload?: any;
};

type FormValues = Omit<PublicSettingsPayload, 'conditions'> & {
  conditions: Record<string, any>;
};

const UPDATE_SETTINGS_DELAY = 5000;
const PROFILE_DOWNLOAD = 'urn:uuid:5b049e82-495f-4335-a083-1e875a8f871b';

type State = {
  description: string;
  header: string;
};

type Action = { type: 'download' } | { type: 'share' };

const reducer = (state: State, action: Action) => {
  if (action.type === 'download') {
    return {
      header: 'Download',
      description:
        'You chose to hide some conditions from your Health Profile, which may be important for your consult with the doctor.',
      // title: 'dowloading',
    };
  }
  if (action.type === 'share') {
    return {
      header: 'Share',
      description:
        'Be Fluent LLP has no control over who accesses the information/record(s) that you are sharing and shall not be liable in any manner for any unauthorised access to the information/record(s) via the shared link. By exporting and sharing this information/record(s) outside the App, you acknowledge that there is a risk of the information/record(s) no longer remaining confidential and secure.',
      // title: 'sharing',
    };
  }
  throw Error('Unknown action.');
};

// https://day.js.org/docs/en/manipulate/add
const EXPIRATION_SELECT_OPTIONS = [
  { label: '1 day', value: '1' },
  { label: '2 days', value: '2' },
  { label: '7 days', value: '7' },
];

const MAIN_SETTINGS_CHECKBOX_LIST: CheckboxItem[] = [
  { label: 'Last name', value: PUBLIC_SETTINGS_PROPERTY_NAME.SHOW_LAST_NAME },
  { label: 'Emergency Contact', value: PUBLIC_SETTINGS_PROPERTY_NAME.EMERGENCY_CONTACT_SECTION },
  { label: 'Alternative Medical Decision-Maker', value: PUBLIC_SETTINGS_PROPERTY_NAME.HEALTHCARE_PROXY_SECTION },
  { label: 'Health Insurance', value: PUBLIC_SETTINGS_PROPERTY_NAME.HEALTH_INSURANCE_SECTION },
  // { label: 'Family Member', value: PUBLIC_SETTINGS_PROPERTY_NAME.FAMILY_MEMBER_HISTORY_SECTION },
  { label: 'Conditions', value: PUBLIC_SETTINGS_PROPERTY_NAME.SHOW_CONDITIONS },
];

const DEFAULT_FORM_VALUES: FormValues = {
  [PUBLIC_SETTINGS_PROPERTY_NAME.EXPIRES_IN]: EXPIRATION_SELECT_OPTIONS[0].value,
  [PUBLIC_SETTINGS_PROPERTY_NAME.SHOW_LAST_NAME]: true,
  [PUBLIC_SETTINGS_PROPERTY_NAME.EMERGENCY_CONTACT_SECTION]: true,
  [PUBLIC_SETTINGS_PROPERTY_NAME.HEALTHCARE_PROXY_SECTION]: true,
  [PUBLIC_SETTINGS_PROPERTY_NAME.HEALTH_INSURANCE_SECTION]: true,
  [PUBLIC_SETTINGS_PROPERTY_NAME.SHOW_CONDITIONS]: false,
  [PUBLIC_SETTINGS_PROPERTY_NAME.VITALS]: false,
  conditions: {
    [PUBLIC_SETTINGS_PROPERTY_NAME.MY_CONDITIONS]: [],
    [PUBLIC_SETTINGS_PROPERTY_NAME.MY_FAMILY_HISTORY]: [],
    [PUBLIC_SETTINGS_PROPERTY_NAME.MY_SYMPTOMS]: [],
    [PUBLIC_SETTINGS_PROPERTY_NAME.MY_MEDICATIONS]: [],
    [PUBLIC_SETTINGS_PROPERTY_NAME.MY_ALLERGIES_INTOLERANCES]: [],
    [PUBLIC_SETTINGS_PROPERTY_NAME.MY_SURGERIES_PROCEDURES]: [],
    [PUBLIC_SETTINGS_PROPERTY_NAME.MY_PREVENTATIVE_SCREENINGS]: [],
    [PUBLIC_SETTINGS_PROPERTY_NAME.MY_IMMUNIZATIONS]: [],
  },
  [PUBLIC_SETTINGS_PROPERTY_NAME.MY_CONSENTS]: false,
};

// Overwrite select styles
const SELECT_STYLES: StylesConfig = {
  ...BASE_SELECT_STYLES,
  menu: (base, props) => ({
    ...BASE_SELECT_STYLES.menu!(base, props),
    minWidth: '100px',
    maxWidth: '120px',
  }),
  indicatorsContainer: (base, props) => ({
    ...BASE_SELECT_STYLES.indicatorsContainer!(base, props),
    padding: '5px',
  }),
  dropdownIndicator: (base, props) => ({
    ...BASE_SELECT_STYLES.dropdownIndicator!(base, props),
    padding: '0',
    color: colors.gray[200],
  }),
  singleValue: (base, props) => ({
    ...BASE_SELECT_STYLES.singleValue!(base, props),
    fontSize: '16px',
    color: colors.gray[500],
  }),
  valueContainer: (base, props) => ({
    ...BASE_SELECT_STYLES.valueContainer!(base, props),
    minHeight: '28px',
    height: '28px',
    marginTop: '-3px',
    paddingLeft: '8px',
    color: colors.gray[500],
    maxWidth: '120px',
  }),
  control: (base, props) => ({
    ...BASE_SELECT_STYLES.control!(base, props),
    backgroundColor: 'white',
    borderRadius: '8px',
    minHeight: '28px',
    height: '28px',
    border: `1px solid ${colors.gray[100]}`,
    borderTop: `1px solid ${colors.gray[100]}`,
    borderLeft: `1px solid ${colors.gray[100]}`,
    borderRight: `1px solid ${colors.gray[100]}`,
  }),
};

// const selectOptionToExpiryTime = (optionValue: string): string => {
//   const date = dayjs().add(Number(optionValue), 'day');
//   return date.format('YYYY-MM-DD HH:mm:ss');
// };
function CheckboxList({
  checkboxList,
  isCheckboxGroup,
  checkboxGroupName,
  onConditionsModalOpen,
  payload,
  setPayload,
  handleChangeData,
  conditionDisplayText,
  header,
  ...props
}: ChakraProps & {
  checkboxList: CheckboxItem[];
  isCheckboxGroup?: boolean;
  checkboxGroupName?: string;
  onConditionsModalOpen?: any;
  payload: any[];
  setPayload: any;
  handleChangeData: (data: any, payload: any[], setPayload: any, isChecked: boolean) => void;
  conditionDisplayText?: string;
  header: string;
}) {
  const theme = useTheme();
  const { control } = useFormContext();

  const getSelectedValues = () => {
    return checkboxList
      .filter((item) => {
        return payload.some((x) =>
          header === 'Download'
            ? x.id?.includes(item.value) && x.isChecked
            : x?.reference?.reference?.includes(item.value) && x.isChecked
        );
      })
      .map((item) => item.value);
  };

  return (
    <Flex
      direction="column"
      padding="22px 24px"
      bgColor={hexOpacity(theme.colors.fluentHealthComplementary.Salmon2, 0.2)}
      border="1px solid"
      borderColor="fluentHealth.500"
      borderRadius="12px"
      {...props}
    >
      {isCheckboxGroup && checkboxGroupName ? (
        <Controller
          name={checkboxGroupName}
          control={control}
          render={({ field: { onChange, value, ...field } }) => (
            <CheckboxGroup
              {...field}
              value={getSelectedValues()}
              onChange={(selectedValues) => {
                checkboxList.forEach((item) => {
                  const isChecked = selectedValues.includes(item.value);
                  handleChangeData(item, payload, setPayload, isChecked);
                });
              }}
            >
              {checkboxList.map((item, index) => {
                const isCheckedValue = getSelectedValues().includes(item.value);

                return (
                  <React.Fragment key={item.value}>
                    <Flex
                      key={item.label}
                      as={chakra.label}
                      justify="space-between"
                      align="center"
                      py="16px"
                      cursor="pointer"
                      onClick={item.value === 'show_conditions' ? onConditionsModalOpen : () => {}}
                      _first={{ pt: 0 }}
                      _last={{ pb: 0 }}
                    >
                      <Flex flexDirection="column">
                        <Text fontSize="lg">{item.label}</Text>
                        {item.value === 'show_conditions' && <Text color="charcoal.60">{conditionDisplayText}</Text>}
                      </Flex>
                      {item.value === 'show_conditions' ? (
                        <RightCircleArrow />
                      ) : (
                        <Checkbox
                          variant="toggle"
                          isChecked={isCheckedValue}
                          onChange={() => {
                            handleChangeData(item, payload, setPayload, !isCheckedValue);
                          }}
                        />
                      )}
                    </Flex>
                    {index < checkboxList.length - 1 && <Divider />}
                  </React.Fragment>
                );
              })}
            </CheckboxGroup>
          )}
        />
      ) : (
        checkboxList.map((item, index) => (
          <React.Fragment key={item.value}>
            <Controller
              name={item.value}
              control={control}
              render={({ field }) => {
                const isChecked =
                  header === 'Download'
                    ? !!payload.find((x) => x?.type?.coding[0]?.code === item.value)
                    : !!payload.find((x) => x?.meaning === item.value);
                return (
                  <Flex
                    key={item.label}
                    as={chakra.label}
                    justify="space-between"
                    align="center"
                    py="16px"
                    onClick={item.value === 'show_conditions' ? onConditionsModalOpen : () => {}}
                    cursor="pointer"
                    _first={{ pt: 0 }}
                    _last={{ pb: 0 }}
                  >
                    <Flex flexDirection="column">
                      <Text fontSize="lg">{item.label}</Text>
                      {item.value === 'show_conditions' && <Text color="charcoal.60">{conditionDisplayText}</Text>}
                    </Flex>
                    {item.value === 'show_conditions' ? (
                      <RightCircleArrow />
                    ) : (
                      <Checkbox
                        variant="toggle"
                        {...field}
                        isChecked={isChecked}
                        onChange={(e) => {
                          handleChangeData(item, payload, setPayload, e.target.checked);
                        }}
                      />
                    )}
                  </Flex>
                );
              }}
            />
            {index < checkboxList.length - 1 && <Divider />}
          </React.Fragment>
        ))
      )}
    </Flex>
  );
}

function MyConditionsSettings({
  checkboxList,
  onChangeAll,
  payload,
  setPayload,
  handleChangeData,
  conditionDisplayText,
  formDataObject,
  header,
}: {
  checkboxList: CheckboxItem[];
  onChangeAll: () => void;
  payload: any;
  setPayload: any;
  handleChangeData: (data: any, payload: any[], setPayload: any, isChecked: boolean) => void;
  conditionDisplayText: string;
  formDataObject: (data: CheckboxItem, isChecked?: boolean) => {};
  header: string;
}) {
  const allChecked = useMemo(() => {
    return checkboxList.every((item) =>
      payload.some((p: any) =>
        header === 'Download'
          ? p.id === item.payload?.id && p.isChecked
          : p.reference?.reference?.includes(item.value) && p.isChecked
      )
    );
  }, [payload, checkboxList, header]);

  const selectAllHandler = () => {
    if (allChecked) {
      setPayload((prev: any[]) =>
        prev.filter((p: any) =>
          header === 'Download'
            ? !checkboxList.some((item) => item.payload?.id === p.id)
            : !checkboxList.some((item) => p.reference?.reference?.includes(item.value))
        )
      );
    } else {
      const newPayload = checkboxList.map((item) => ({
        ...formDataObject(item),
        isChecked: true,
      }));
      setPayload((prev: any[]) => [
        ...prev.filter((p: any) =>
          header === 'Download'
            ? !checkboxList.some((item) => item.payload?.id === p.id)
            : !checkboxList.some((item) => p.reference?.reference?.includes(item.value))
        ),
        ...newPayload,
      ]);
    }
    onChangeAll();
  };

  return (
    <Box>
      <Flex
        justify="space-between"
        mb="12px"
        mt="32px"
      >
        <Text
          fontSize="xs"
          fontWeight="500"
          textTransform="uppercase"
        >
          My Conditions
        </Text>
        <Button
          variant="quiet"
          height="auto"
          color="iris.500"
          fontSize="md"
          onClick={selectAllHandler}
        >
          {allChecked ? 'Hide all' : 'Share all'}
        </Button>
      </Flex>
      <CheckboxList
        checkboxList={checkboxList}
        checkboxGroupName={`conditions.${PUBLIC_SETTINGS_PROPERTY_NAME.MY_CONDITIONS}`}
        isCheckboxGroup
        padding="16px"
        payload={payload}
        setPayload={setPayload}
        handleChangeData={handleChangeData}
        conditionDisplayText={conditionDisplayText}
        header={header}
      />
    </Box>
  );
}
// function FamilyHistoryConditionsSettings({
//   checkboxList,
//   onChangeAll,
// }: {
//   checkboxList: CheckboxItem[];
//   onChangeAll: () => void;
// }) {
//   const form = useFormContext();

//   const selectAllHandler = () => {
//     form.setValue(
//       `conditions.${PUBLIC_SETTINGS_PROPERTY_NAME.MY_FAMILY_HISTORY}`,
//       checkboxList.map((item) => item.value)
//     );
//     onChangeAll();
//   };

//   return (
//     <Box>
//       <Flex
//         justify="space-between"
//         mb="12px"
//       >
//         <Text
//           fontSize="xs"
//           fontWeight="500"
//           textTransform="uppercase"
//         >
//           Family History
//         </Text>
//         <Button
//           variant="quiet"
//           height="auto"
//           color="iris.500"
//           fontSize="md"
//           onClick={selectAllHandler}
//         >
//           Select all
//         </Button>
//       </Flex>
//       <CheckboxList
//         checkboxList={checkboxList}
//         checkboxGroupName={`conditions.${PUBLIC_SETTINGS_PROPERTY_NAME.MY_FAMILY_HISTORY}`}
//         isCheckboxGroup
//         padding="16px"
//       />
//     </Box>
//   );
// }

function MainSettings({
  checkboxList,
  onConditionsModalOpen,
  payload,
  setPayload,
  handleChangeData,
  conditionDisplayText,
  header,
}: {
  checkboxList: CheckboxItem[];
  onConditionsModalOpen: any;
  payload: any[];
  setPayload: any;
  handleChangeData: (data: any, payload: any[], setPayload: any, isChecked: boolean) => void;
  conditionDisplayText: string;
  header: string;
}) {
  return (
    <>
      <Flex
        justify="space-between"
        alignItems="center"
      >
        <Text fontSize="xl">{`You can choose to share/hide the following when ${header?.toLowerCase()}ing your Health Profile.`}</Text>
      </Flex>
      <Text
        color="gray.400"
        my="12px"
      >
        Turn on to share, turn off to hide.
      </Text>
      <CheckboxList
        checkboxList={checkboxList}
        onConditionsModalOpen={onConditionsModalOpen}
        payload={payload}
        setPayload={setPayload}
        handleChangeData={handleChangeData}
        conditionDisplayText={conditionDisplayText}
        header={header}
      />
    </>
  );
}

type ProfileSharingFormProps = {
  header: string;
};

function ProfileSharingForm({ header }: ProfileSharingFormProps) {
  const [publicUrl, setPublicUrl] = useState<string>();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isDisabled, setIsDisabled] = useState<boolean>(false);
  const [generateLink, setGenerateLink] = useState<boolean>(false);
  const [mainPayload, setMainPayload] = useState<any[]>([]);
  const [conditionPayload, setConditionPayload] = useState<any[]>([]);
  const { authenticatedUser } = useAuthService();
  const { patient } = usePatient(authenticatedUser?.id);

  const theme = useTheme();
  const pollingRef: any = useRef(null);

  const toast = useToast();
  const conditionsModal = useModal();
  const client = useClient();

  const { answerList: conditionList } = useMasterConditionResponseList(authenticatedUser?.id);
  const { insuranceList } = useHealthInsuranceList(authenticatedUser?.id);
  const { emergencyContactList } = useEmergencyContactList(authenticatedUser?.id);
  const { healthcareProxyList } = useHealthcareProxyList(authenticatedUser?.id);
  const { createTaskForShare }: any = useShareProfileRecord(authenticatedUser?.id);
  // const { familyMemberList } = useFamilyMemberHistoryList(authenticatedUser?.id);
  const form = useForm<FormValues>({
    mode: 'onChange',
    defaultValues: DEFAULT_FORM_VALUES,
  });
  const conditionsField = form.watch(PUBLIC_SETTINGS_PROPERTY_NAME.SHOW_CONDITIONS);
  const expirationTimeField = form.watch(PUBLIC_SETTINGS_PROPERTY_NAME.EXPIRES_IN);

  const { trackEventInFlow } = useAnalyticsService<ProfileConditionAnalyticsProps>();
  // My conditions to checkbox list
  const myConditionsCheckboxList = useMemo<CheckboxItem[]>(
    () =>
      conditionList.map((condition: any) => {
        return {
          label: condition?.code?.coding?.[0]?.display,
          value: condition.id,
          checked:
            condition?.meta?.tag?.find(
              (tag: { system: string; display: string }) => tag.system === FH_CODE_SYSTEM_FLUENT_HEALTH_UI
            )?.display === 'True',
          payload: condition,
        };
      }),
    [conditionList]
  );
  // Clear Polling when modal is closed
  useEffect(() => {
    return () => {
      clearInterval(pollingRef.current);
    };
  }, []);

  const mainSettingsCheckboxList = useMemo(() => {
    const filterBy: string[] = [];

    if (
      myConditionsCheckboxList.length === 0
      // && familyHistoryConditionsCheckboxList.length === 0
    ) {
      filterBy.push(PUBLIC_SETTINGS_PROPERTY_NAME.SHOW_CONDITIONS);
    }
    if (emergencyContactList.length === 0) {
      filterBy.push(PUBLIC_SETTINGS_PROPERTY_NAME.EMERGENCY_CONTACT_SECTION);
    }
    if (healthcareProxyList.length === 0) {
      filterBy.push(PUBLIC_SETTINGS_PROPERTY_NAME.HEALTHCARE_PROXY_SECTION);
    }
    if (insuranceList.QuestionnaireResponseList.length === 0) {
      filterBy.push(PUBLIC_SETTINGS_PROPERTY_NAME.HEALTH_INSURANCE_SECTION);
    }
    // if (familyMemberList?.FamilyMemberHistoryList.length === 0) {
    //   filterBy.push(PUBLIC_SETTINGS_PROPERTY_NAME.FAMILY_MEMBER_HISTORY_SECTION);
    // }
    return MAIN_SETTINGS_CHECKBOX_LIST.filter((item) => !filterBy.includes(item.value));
  }, []);
  const updatePublicUrl = async () => {
    const filteredConditionPayload = conditionPayload.map(({ id, isChecked, ...rest }) => rest);
    console.log({ filteredConditionPayload, conditionPayload });

    const data = await createTaskForShare({
      type: 'share-profile',
      data: mainPayload,
      code:
        filteredConditionPayload.length > 0
          ? [
              {
                coding: filteredConditionPayload.map((item: any) => item.code),
              },
            ]
          : null,
      daysToExpire: expirationTimeField,
    });
    pollingRef.current = setInterval(async () => {
      const urlData: any = await medplumApi.shareProfileRecord.getShareProfileURL(data.entry[1].resource.id);
      if (urlData?.Task?.output?.length > 0) {
        clearInterval(pollingRef.current);
        setPublicUrl(urlData?.Task?.output?.[0]?.valueString);
        setIsLoading(false);

        trackEventInFlow(AnalyticsFlow.ShareProfileLinkGenerated, AnalyticsEventName.ShareProfileLinkGenerated, {});
      }
    }, UPDATE_SETTINGS_DELAY); // Poll every UPDATE_SETTINGS_DELAY
  };

  const generateFile = async (data: any) => {
    const payload = {
      resourceType: 'Bundle',
      type: 'transaction',
      entry: [
        {
          fullUrl: PROFILE_DOWNLOAD,
          resource: {
            resourceType: 'Task',
            status: 'requested',
            statusReason: {
              coding: [
                {
                  code: 'Generate Profile PDF',
                  display: 'Generate Profile PDF',
                },
              ],
            },
            intent: 'option',
            priority: 'routine',
            for: {
              reference: `Patient/${authenticatedUser?.id}`,
              type: 'Patient',
            },
            code: {
              coding: [
                {
                  code: 'downloadpdf-profile',
                  display: 'downloadpdf-profile',
                },
              ],
            },
            description: 'Generate PDF',
            input: data,
          },
          request: {
            method: 'POST',
            url: 'Task',
          },
        },
      ],
    };
    const response = await medplumApi.downloadProfile.createDownloadProfile(payload);
    return response;
  };

  const downloadFile = useCallback(
    async (fileUrl: string) => {
      try {
        // Get the user's first name for the filename
        let firstName = '';
        if (patient) {
          const fullName = parsePatientName(patient?.name);
          [firstName] = fullName.split(' ');
        }
        const safeName = firstName ? `${firstName}’s Health Profile.pdf` : 'My Health Profile.pdf';
        await downloadFileUtil(fileUrl, safeName);
        setIsLoading(false);
        setIsDisabled(false);
        conditionsModal.modalDisclosure.onClose();
      } catch (error) {
        toast({
          title: 'Something went wrong! Try again later.',
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
      } finally {
        conditionsModal.modalDisclosure.onClose();
      }
    },
    [patient]
  );

  const getProfile = async () => {
    setIsLoading(true);
    setIsDisabled(true);
    const filteredConditionPayload = conditionPayload
      .filter((item) => item.isChecked === true)
      .map(({ id, isChecked, ...rest }) => rest);

    const data = await generateFile([...mainPayload, ...filteredConditionPayload]);
    let taskData: any = {};
    pollingRef.current = setInterval(async () => {
      try {
        taskData = data?.entry?.[0]?.resource?.id && (await downloadProfile(client, data?.entry?.[0]?.resource?.id));
        if (!taskData) {
          toast({
            title: 'Something went wrong! Try again later.',
            status: 'error',
            duration: 3000,
            isClosable: true,
          });
          clearInterval(pollingRef.current);
          setIsLoading(false);
        }
      } catch (err) {
        toast({
          title: 'Something went wrong! Try again later.',
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
        clearInterval(pollingRef.current);
        setIsLoading(false);
      }
      if (taskData?.status === 'failed') {
        toast({
          title: 'Something went wrong! Try again later.',
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
        clearInterval(pollingRef.current);
        setIsLoading(false);
      }
      if (taskData?.status === 'completed') {
        clearInterval(pollingRef.current);
        const parts = taskData?.output[0]?.valueReference?.reference?.split('/');
        const documentReference = parts && parts[parts.length - 1];
        const fileData = await downloadFileQuery(client, documentReference);
        const url = fileData?.content?.[0]?.attachment?.url;
        // eslint-disable-next-line @typescript-eslint/no-unused-expressions
        documentReference && downloadFile(url);
      }
    }, UPDATE_SETTINGS_DELAY);
  };

  const onConditionsModalClose = async (onSave?: boolean) => {
    conditionsModal.modalDisclosure.onClose();
    if (onSave) {
      return conditionPayload.filter((item) => item.isChecked === true);
    }
    return [];
  };
  const onConditionsModalOpen = () => {
    conditionsModal.modalDisclosure.onOpen();
  };
  const copyToClipboardHandler = async (value: string) => {
    try {
      setIsLoading(true);
      await navigator.clipboard.writeText(value);
      toast({
        title: 'Successfully copied',
        status: 'success',
        duration: 1500,
        isClosable: true,
      });
    } catch (err) {
      toast({
        title: (err as any).message,
        status: 'error',
        duration: 4000,
        isClosable: true,
      });
    } finally {
      setTimeout(setIsLoading, 500, false);
    }
  };

  const onGenerateLink = () => {
    setGenerateLink(true);
    setIsLoading(true);
    setIsDisabled(true);
    updatePublicUrl();
  };

  const onDownload = async () => {
    setIsLoading(true);
    getProfile();
  };

  const onExpiryTimeSelectChange = async (option: SelectOptionProps | any) => {
    form.setValue(PUBLIC_SETTINGS_PROPERTY_NAME.EXPIRES_IN, option.value);
    await form.trigger(PUBLIC_SETTINGS_PROPERTY_NAME.EXPIRES_IN);
    setIsDisabled(false);
  };
  function getSnomedCode(payload: any) {
    const snomed = payload?.code?.coding.find((c: any) => c?.system === 'http://snomed.info/sct');

    if (!snomed) return null;

    return {
      code: snomed?.code,
      display: snomed?.display,
    };
  }
  const formDataObject = (data: any, isChecked?: boolean) => {
    const dataisChecked =
      data?.payload?.meta?.tag?.find(
        (tag: { system: string; display: string }) =>
          tag.system === 'https://fluentinhealth.com/FHIR/CodeSystem/FluentHealthMetaTag'
      )?.display === 'True'
        ? true
        : false || isChecked || data.checked;
    if (header === 'Download') {
      return {
        id: data?.payload?.id,
        isChecked: dataisChecked,
        type: {
          coding: [
            {
              code: Object.hasOwn(data, 'payload') ? 'condition' : data.value,
              display: Object.hasOwn(data, 'payload') ? 'condition' : data.value,
            },
          ],
        },
        valueString: data?.payload
          ? `Condition/${data?.payload?.id}`
          : `Patient/${authenticatedUser?.id}${data.value === 'lastName' ? `.name.family` : ''}`,
      };
    }
    return {
      id: data?.payload?.id,
      isChecked: dataisChecked,
      meaning: Object.hasOwn(data, 'payload') ? 'condition' : data.value,
      reference: {
        reference: data?.payload
          ? `Condition/${data?.payload?.id}`
          : `Patient/${authenticatedUser?.id}${data.value === 'lastName' ? `.name.family` : ''}`,
      },
      code: data?.payload ? getSnomedCode(data?.payload) : undefined,
    };
  };

  const handleChangeData = (data: any, payload: any, setPayload: any, isChecked: boolean) => {
    const eventFlag = {
      value: '',
      flag: false,
    };

    const defaultFamilyHistory = {
      type: {
        coding: [
          {
            code: 'familyMemberHistory',
            display: 'familyMemberHistory',
          },
        ],
      },
      valueString: `Patient/${patient}`,
    };

    // Check if `header` is 'Download' and if `data` doesn't have 'payload'
    if (header === 'Download') {
      if (!Object.hasOwn(data, 'payload')) {
        // Handle the case where data has no 'payload'
        if (payload.findIndex((payloadVal: any) => payloadVal.type.coding[0].code === data.value) === -1) {
          eventFlag.value = data.value;
          eventFlag.flag = true;
          setPayload([...payload, { ...formDataObject(data) }, { ...defaultFamilyHistory }]); // Add to payload
        } else {
          // If the item exists, update the isChecked flag (or other properties as needed)
          eventFlag.value = data.value;
          eventFlag.flag = false;
          setPayload((prev: any[]) => [
            ...prev.filter((filteredVal: any) => filteredVal.type.coding[0].code !== data.value),
            { ...defaultFamilyHistory },
          ]); // Remove from payload
        }
      } else if (payload.findIndex((p: any) => p.valueString.includes(data?.payload?.id)) === -1) {
        // Added to condition based on payload ID

        eventFlag.value = payload?.isChecked;
        eventFlag.flag = true;

        setPayload([...payload, { ...formDataObject(data), isChecked }, { ...defaultFamilyHistory }]);
      } else {
        eventFlag.value = payload?.isChecked;
        eventFlag.flag = false;
        setPayload((prevPayload: any[]) => {
          return [
            ...prevPayload.map((item: { valueString: string | any[]; isChecked: any }) => {
              if (item.valueString.includes(data?.payload?.id)) {
                return {
                  ...item,
                  isChecked,
                };
              }
              return item;
            }),
            { ...defaultFamilyHistory },
          ];
        });
      }
    } else if (!Object.hasOwn(data, 'payload')) {
      if (payload.findIndex((payloadVal: any) => payloadVal.meaning === data.value) === -1) {
        eventFlag.value = data.value;
        eventFlag.flag = true;
        setPayload([...payload, { ...formDataObject(data, isChecked) }]);
      } else {
        eventFlag.value = data.value;
        eventFlag.flag = false;
        setPayload((prev: any[]) => prev.filter((filteredVal: any) => filteredVal.meaning !== data.value));
      }
    } else if (payload.findIndex((p: any) => p.reference.reference.includes(data.value)) === -1) {
      // added to condition
      eventFlag.value = payload?.isChecked;
      eventFlag.flag = true;

      setPayload([...payload, { ...formDataObject(data, isChecked) }]);
    } else {
      eventFlag.value = payload?.isChecked;
      eventFlag.flag = false;
      setPayload((prevPayload: any[]) => {
        return prevPayload.map((item: { reference: string | { reference: string }; isChecked: any }) => {
          // Check if item.reference is an object and has a reference property
          if (
            typeof item.reference === 'object' &&
            item.reference !== null &&
            'reference' in item.reference &&
            typeof item.reference.reference === 'string' &&
            item.reference.reference.includes(data?.payload?.id)
          ) {
            return {
              ...item,
              isChecked,
            };
          }

          return item;
        });
      });
    }
  };

  useEffect(() => {
    const prepMainPayload = mainSettingsCheckboxList
      .filter((cArr) => cArr.value !== 'show_conditions')
      .map((pArr) => formDataObject(pArr));
    setMainPayload(prepMainPayload);

    // Prepare condition payload (only items with condition-share-profile as true)
    const prepConditionPayload = myConditionsCheckboxList.map((condArr) => {
      const formData = formDataObject(condArr);
      return formData;
    });

    // Remove all items with only true or false values
    const filteredConditionPayload = prepConditionPayload.filter((item) => {
      const hasMixedValues = Object.values(item).some((value) => value !== true && value !== false);
      return hasMixedValues;
    });

    setConditionPayload(filteredConditionPayload);
  }, []);

  // If the Conditions checked, open the Conditions modal,
  // otherwise reset myConditions and myFamilyHistory.
  useUpdateEffect(() => {
    if (conditionsField) {
      conditionsModal.modalDisclosure.onOpen();
    } else {
      form.setValue(`conditions.${PUBLIC_SETTINGS_PROPERTY_NAME.MY_CONDITIONS}`, []);
      form.setValue(`conditions.${PUBLIC_SETTINGS_PROPERTY_NAME.MY_FAMILY_HISTORY}`, []);
    }
  }, [conditionsField]);
  // Trigger the public url generation when click on Select all.
  const onChangeAll = () => setIsDisabled(false);
  const conditionSharedCount = conditionPayload.filter((item) => item.isChecked === true).length;

  return (
    <FormProvider {...form}>
      <form
        style={{ display: 'flex', flexDirection: 'column', paddingBottom: '30px' }}
        onChange={onChangeAll}
      >
        {isLoading ? (
          <FluentHealthLoader my="12" />
        ) : (
          <>
            <MainSettings
              checkboxList={mainSettingsCheckboxList}
              onConditionsModalOpen={onConditionsModalOpen}
              payload={mainPayload}
              setPayload={setMainPayload}
              handleChangeData={handleChangeData}
              conditionDisplayText={`${conditionSharedCount} out of ${myConditionsCheckboxList.length} conditions shared`}
              header={header}
            />
            <Button
              onClick={header === 'Download' ? onDownload : onGenerateLink}
              margin="32px 0px"
              isDisabled={isDisabled}
              padding="12px 10px"
              justifyContent="center"
            >
              {header === 'Download' ? 'Download' : 'Generate link'}
            </Button>
            {generateLink && (
              <>
                <CopyToClipboardInput
                  value={publicUrl}
                  onCopy={() => {
                    copyToClipboardHandler(publicUrl!);
                    trackEventInFlow(AnalyticsFlow.ShareProfileCompleted, AnalyticsEventName.ShareProfileCompleted, {
                      [EventPropsNames.LinkActiveDuration]: `${expirationTimeField} days`,
                      [EventPropsNames.ProfileSectionsShared]: `${mainPayload.map((val) => val.meaning).join(', ')}.`,
                      [EventPropsNames.AllConditionsShared]:
                        conditionPayload.length === myConditionsCheckboxList.length,
                    });
                  }}
                  isLoading={isLoading}
                  bgColor="periwinkle.50"
                  border="none"
                  borderRadius="12px"
                />
                {/* <Flex
                  direction="row"
                  gap="4px"
                  alignItems="center"
                  mt="16px"
                  mb="32px"
                >
                  <Text
                    fontFamily="Apercu"
                    fontWeight="normal"
                    fontSize="sm"
                    lineHeight="1"
                    color="fluentHealthText.200"
                  >
                    Link expires in
                  </Text>
                  <Select
                    value={EXPIRATION_SELECT_OPTIONS.find((option) => option.value === expirationTimeField)}
                    options={EXPIRATION_SELECT_OPTIONS}
                    onChange={onExpiryTimeSelectChange}
                    styles={SELECT_STYLES}
                    isSearchable={false}
                  />
                </Flex> */}
              </>
            )}
            <Modal
              variant={MODAL_VARIANTS.SHARING}
              title="Conditions"
              showCloseButton={false}
              showModalFooter
              showPrimaryButton={false}
              showSecondaryButton={false}
              modalContentProps={{
                className: 'hide-scrollbar',
                overflowY: 'auto',
              }}
              titleFontSize="xl"
              footerAlert={
                <Alert
                  variant="warning"
                  bgColor="periwinkle.100"
                  width="calc(100% + 48px)"
                  borderRadius="0px"
                  margin="-24px"
                  p="20px 24px"
                >
                  <AlertIcon
                    size={20}
                    color={theme.colors.periwinkle[600]}
                    style={{ flexShrink: 0, marginTop: '-44px' }}
                  />
                  <AlertDescription color={theme.colors.periwinkle[600]}>
                    You chose to hide some conditions from the Health Profile, which may be important for your consult
                    with the doctor.
                  </AlertDescription>
                </Alert>
              }
              minWidth="420px"
              isCentered
              {...conditionsModal.modalProps}
              {...conditionsModal.modalDisclosure}
              onClose={onConditionsModalClose}
            >
              <ModalCloseButton
                style={{ height: '28px', width: '28px' }}
                onClick={() => onConditionsModalClose()}
              />
              <Stack spacing="32px">
                {myConditionsCheckboxList.length > 0 && (
                  <MyConditionsSettings
                    checkboxList={myConditionsCheckboxList}
                    onChangeAll={onChangeAll}
                    payload={conditionPayload}
                    setPayload={setConditionPayload}
                    handleChangeData={handleChangeData}
                    conditionDisplayText={`${conditionPayload.length} out of ${myConditionsCheckboxList.length} conditions shared`}
                    formDataObject={formDataObject}
                    header={header}
                  />
                )}
                <Button
                  onClick={() => onConditionsModalClose(true)}
                  style={{
                    marginBottom: '32px',
                  }}
                >
                  Save
                </Button>
              </Stack>
            </Modal>
          </>
        )}
      </form>
    </FormProvider>
  );
}

export function ProfileSharingFlow() {
  const theme = useTheme();
  const sharingModal = useModal();
  const isMobile = useIsMobile();

  const { trackEventInFlow } = useAnalyticsService<ProfileConditionAnalyticsProps>();
  const [state, dispatch] = useReducer(reducer, { description: '', header: '' });
  const { isPublicMode } = usePublicSettings();

  if (isPublicMode) {
    return null;
  }
  return (
    <>
      <Flex
        display="flex"
        flexDirection="row"
        gap={isMobile ? '8px' : '24px'}
        justifyContent={isMobile ? 'center' : 'right'}
        margin={isMobile ? '20px' : '24px'}
      >
        <Button
          variant="outline"
          color="#FFF"
          onClick={() => {
            dispatch({ type: 'download' });
            trackEventInFlow(AnalyticsFlow.DownloadProfileClicked, AnalyticsEventName.DownloadProfileClicked, {
              // [EventPropsNames.EntryPoint]: 'Health Profile download button',
              // [EventPropsNames.ScreenName]: 'Health Profile screen',
              // [EventPropsNames.DownloadProfileButtonClicked]: true,
            });
            sharingModal.modalDisclosure.onOpen();
          }}
          rightIcon={<DownloadIcon />}
        >
          {isMobile ? 'Download' : 'Download Profile'}
        </Button>

        <Button
          variant="outline"
          color="#FFF"
          onClick={() => {
            dispatch({ type: 'share' });
            // trackEventInFlow(AnalyticsFlow.ShareProfileStarted, AnalyticsEventName.ShareProfileStarted, {
            //   [EventPropsNames.EntryPoint]: 'Health Profile share button',
            //   [EventPropsNames.ScreenName]: 'Health Profile screen',
            //   [EventPropsNames.TabName]: currentTab,
            // });
            sharingModal.modalDisclosure.onOpen();
          }}
          rightIcon={<ShareIcon />}
        >
          {isMobile ? 'Share' : 'Share Profile'}
        </Button>
      </Flex>
      <Modal
        variant={MODAL_VARIANTS.PERIWINKLE}
        title={`${state.header} your Health Profile`}
        showPrimaryButton={false}
        showSecondaryButton={false}
        footerAlert={
          <Alert
            variant="warning"
            bgColor="periwinkle.100"
            width="calc(100% + 48px)"
            borderRadius="0px"
            margin="-24px"
            p="20px 24px"
          >
            <AlertIcon
              size={20}
              color={theme.colors.periwinkle[600]}
              style={{ flexShrink: 0, marginTop: '0' }}
            />
            <AlertDescription color={theme.colors.periwinkle[600]}>{state.description}</AlertDescription>
          </Alert>
        }
        modalContentProps={{
          className: 'hide-scrollbar',
          overflowY: 'auto',
        }}
        minWidth="600px"
        isCentered
        {...sharingModal.modalProps}
        {...sharingModal.modalDisclosure}
      >
        <Box pt="24px">
          <Suspense fallback={<FluentHealthLoader my="12" />}>
            <ProfileSharingForm header={state.header} />
          </Suspense>
        </Box>
      </Modal>
    </>
  );
}
