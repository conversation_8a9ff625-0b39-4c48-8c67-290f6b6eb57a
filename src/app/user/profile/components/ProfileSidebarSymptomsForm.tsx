import {
  Box,
  Button,
  Flex,
  FormControl,
  FormErrorMessage,
  FormLabel,
  HStack,
  Input,
  Stack,
  Switch,
  Text,
  useDisclosure,
  useOutsideClick,
  useToast,
} from '@chakra-ui/react';
import { RefObject, useCallback, useEffect, useRef, useState } from 'react';
import dayjs from 'dayjs';
import { FormProvider, useForm, useFormContext } from 'react-hook-form';
import { useSymptomsList } from '@user/lib/medplum-state';
import { recordSymptomsEvents } from '@user/lib/events-analytics-manager';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useNavigate } from 'react-router-dom';
import { DOCUMENT_REF, NavigationHelper, PATIENT_DEFINED } from '@user/lib/constants';
import { medplumApi } from '@user/lib/medplum-api';

import { Symptom } from '@lib/models/symptoms';
import { DatePickerField } from '../../../../components/ui/Form';
import { AttachedMedicalRecord } from '@lib/models/medical-record';
import { MedicalRecordSelect } from './MedicalRecordSelect';
import { LinkedDocumentsCard } from './LinkedDocumentsCard';
import { SearchableSelect, SelectOptionProps } from '../../../../components/ui/Select';
import { useAnalyticsService, useAuthService } from '@lib/state';
import {
  FH_CODE_SYSTEM_FLUENT_HEALTH_UI,
  FH_STRUCTURE_DEFINITION_CLINICALSTATUS,
  SNOMED_URL,
} from 'src/constants/medplumConstants';
import { FACT_CODE_SYSTEM, FHIR_CODE_SYSTEM_FACT_URL } from '@lib/constants';
import { formatDateForSave, isDuplicatePresentSymptom, isEmptyObject } from '@lib/utils/utils';
import { useExtractDocumentResource } from 'src/app/medical-records/lib/state';

type SymptomFormValues = Omit<Symptom, 'start_date' | 'end_date' | 'id' | 'symptom'> & {
  start_date: string;
  end_date: string | null;
  id: string | null;
  symptom: { value: string; label: string } | null;
  custom_symptom?: string;
  clinicalStatus?: boolean;
  notes?: string;
  symptoms_severity: { value: string; label: string } | null;
  external_reports: AttachedMedicalRecord[];
};
const MAX_ALLOWED_CHARACTERS = 500;

const customOption = { label: 'My symptom is not listed', value: 'MY_SYMPTOM_NOT_LISTED' };
export function NoOptionsMessageComponent({ onClick }: { onClick: () => void }) {
  return (
    <Text
      sx={{
        width: '100%',
        textAlign: 'left',
        color: 'fluentHealthText.100',
        cursor: 'pointer',
      }}
      onClick={onClick}
    >
      My symptom is not listed
    </Text>
  );
}

function getInitialFormData(symptom: any | null): SymptomFormValues {
  const { id = '', effectivePeriod, code, note, derivedFrom = [], valueString } = symptom || {};
  const primary = code?.coding?.[0] ?? { code: '', display: '' };
  const isCustom = primary.code === `sym:${PATIENT_DEFINED}`;
  return {
    id,
    symptom: {
      value: isCustom ? customOption.value : primary.code,
      label: isCustom ? customOption.label : primary.display,
    },
    notes: note?.[0]?.text || '',
    start_date: dayjs(effectivePeriod?.start).isValid() ? effectivePeriod?.start : '',
    custom_symptom: isCustom ? primary.display : '',
    clinicalStatus: symptom?.extension?.[0]?.valueCodeableConcept?.coding?.[0]?.code === 'active',
    end_date: dayjs(effectivePeriod?.end).isValid() ? effectivePeriod?.end : '',
    symptoms_severity: valueString ? { value: valueString, label: valueString } : null,
    external_reports: derivedFrom ? useExtractDocumentResource(derivedFrom) : [],
  };
}

export function SymptomSelect({
  symptomOptions,
  onAfterSelect,
  setShowCustomInput,
}: {
  symptomOptions: SelectOptionProps[];
  onAfterSelect?: (value: SelectOptionProps) => void;
  setShowCustomInput: (flag: boolean) => void;
}): JSX.Element {
  const form: any = useFormContext();
  const [symptomValue, setSymptomValue] = useState<SelectOptionProps | null>(form.watch('symptom'));
  const [menuIsOpen, setMenuIsOpen] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const handleNoOptionsClick = useCallback(() => {
    setSymptomValue(customOption);
    setShowCustomInput(true);
    setInputValue('');
    setMenuIsOpen(false);
    form.setValue('symptom', customOption);
  }, [setShowCustomInput]);

  const handleChange = useCallback(
    (option: any) => {
      if (option) {
        form.setValue('symptom', { value: option.value, label: option.label });
        form.trigger('symptom');
        setSymptomValue(option);
      } else {
        form.setValue('symptom', '');
        form.trigger('symptom');
        setSymptomValue(null);
      }
      setShowCustomInput(false);
      onAfterSelect?.(option);
    },
    [form, setShowCustomInput, onAfterSelect]
  );

  const handleFocus = useCallback(() => {
    if (symptomValue?.value === customOption.value) {
      setSymptomValue(null);
      form.setValue('symptom', '');
      setShowCustomInput(false);
    }
  }, [symptomValue, form, setShowCustomInput]);

  const noOptionsMessageFn = useCallback(
    () => <NoOptionsMessageComponent onClick={handleNoOptionsClick} />,
    [handleNoOptionsClick]
  );

  return (
    <FormControl
      variant="floating"
      isInvalid={!!form.formState.errors?.symptom}
    >
      <SearchableSelect
        labelText="Select Symptom*"
        value={symptomValue}
        options={symptomOptions}
        onChange={handleChange}
        onFocus={handleFocus}
        menuIsOpen={menuIsOpen}
        onMenuOpen={() => setMenuIsOpen(true)}
        onMenuClose={() => setMenuIsOpen(false)}
        inputValue={inputValue}
        onInputChange={setInputValue}
        noOptionsMessage={noOptionsMessageFn}
      />
      <FormErrorMessage> {form?.formState?.errors?.symptom?.value?.message} </FormErrorMessage>
    </FormControl>
  );
}

export default function ProfileSidebarSymptomsForm({
  symptom,
  symptomList,
  closeDialog,
  isLoading,
}: {
  symptom: any | null;
  symptomList: SelectOptionProps[];
  closeDialog: () => void;
  isLoading: (loading: boolean) => void;
}) {
  const toast = useToast();
  const navigate = useNavigate();
  const isEditing = !!symptom;
  const [searchTextString] = useState<string>('');
  const [severityOption, setSeverityOption] = useState<SelectOptionProps[]>([]);
  const [isFormValid, setIsFormValid] = useState(false);
  const [showCustomInput, setShowCustomInput] = useState(
    symptom?.code.coding[0].code === `sym:${PATIENT_DEFINED}` || false
  );

  const resultsPopover = useDisclosure();
  const startDatePickerPopover = useDisclosure();
  const endDatePickerPopover = useDisclosure();

  const { authenticatedUser } = useAuthService();
  const { symptomListOfData, addSymptoms, updateSymptoms } = useSymptomsList(authenticatedUser?.id);

  const symptomFormSchema = z.object({
    symptom: z.object({
      value: z
        .string()
        .min(1, 'This is a required field')
        .refine((value) => {
          const duplicateId = isDuplicatePresentSymptom(value, symptomListOfData.ObservationList, symptom);
          return !duplicateId;
        }, 'Symptom already exists'),
      label: z.string(),
    }),
    start_date: z.string().nullable().optional(),
    clinicalStatus: z.boolean().nullable().optional(),
    end_date: z.string().nullable().optional(),
    notes: z.string().max(MAX_ALLOWED_CHARACTERS).optional(),
    custom_symptom: showCustomInput ? z.string().min(1, 'This field is required') : z.string().optional(),
    symptoms_severity: z.object({
      value: z.string().min(1, 'This is a required field'),
      label: z.string().min(1, 'This is a required field'),
    }),
    external_reports: z.array(z.any()).optional(),
  });
  const { trackEventInFlow } = useAnalyticsService();
  const form = useForm<SymptomFormValues>({
    mode: 'onChange',
    defaultValues: getInitialFormData(symptom),
    resolver: zodResolver(symptomFormSchema),
  });

  const {
    handleSubmit,
    formState: { isSubmitting, isValid },
  } = form;

  const startDateField = form.watch('start_date');
  const endDateField = form.watch('end_date');
  const externalReportsField = form.watch('external_reports');
  const symptomInputRef: RefObject<any> = useRef();
  useOutsideClick({
    ref: symptomInputRef,
    handler: () => {
      if (searchTextString?.length > 0) {
        return false;
      }
      resultsPopover.onClose();
      return true;
    },
  });
  const createObservation = (code: string, display: string, startDate: string, endDate: string, linkageToList: any) => {
    const derivedFrom = linkageToList?.map((item: { id: string }) => ({
      reference: `${DOCUMENT_REF}/${item.id}`,
    }));
    const data = {
      resourceType: 'Observation',
      identifier: [
        {
          system: FHIR_CODE_SYSTEM_FACT_URL,
          value: 'FACT-sym',
        },
      ],
      code: {
        coding: [
          {
            system: SNOMED_URL,
            code,
            display,
          },
        ],
      },
      subject: {
        reference: `Patient/${authenticatedUser?.id}`,
      },
      effectivePeriod: {
        start: formatDateForSave(startDate),
        end: formatDateForSave(endDate),
      },
      derivedFrom,
      status: 'final',
    };
    return data;
  };
  const onSubmit = async (addedSymptom: any) => {
    try {
      isLoading(true);
      const entry = [];
      entry.push({
        createObservation,
      });
      let coding: any = [];
      if (addedSymptom?.symptom?.value !== customOption.value) {
        const codeValue = addedSymptom?.symptom?.value;
        coding = await medplumApi.valueSetList.getFHIRCodingFromCMS(codeValue);
      } else {
        coding = [
          {
            system: 'http://fluentinhealth/fact',
            code: `sym:${PATIENT_DEFINED}`,
            display: addedSymptom.custom_symptom,
          },
        ];
      }
      const { value, label } = addedSymptom?.symptom || {};
      const { start_date } = addedSymptom;
      const payload = createObservation(
        value,
        label,
        start_date,
        addedSymptom.clinicalStatus === false ? addedSymptom.end_date ?? undefined : undefined,
        addedSymptom.external_reports
      );

      const payloadSymptom = {
        ...payload,
        id: symptom?.id,
        code: { coding },
        ...(addedSymptom?.notes && { note: [{ text: addedSymptom.notes }] }),
        ...(addedSymptom?.symptoms_severity?.label && { valueString: addedSymptom.symptoms_severity.label }),
      };

      const status = addedSymptom.clinicalStatus
        ? { code: 'active', display: 'Active' }
        : { code: 'inactive', display: 'Inactive' };

      payloadSymptom.extension = [
        {
          url: FH_STRUCTURE_DEFINITION_CLINICALSTATUS,
          valueCodeableConcept: {
            coding: [
              {
                system: FH_CODE_SYSTEM_FLUENT_HEALTH_UI,
                ...status,
              },
            ],
          },
        },
      ];
      if (!isEmptyObject(symptom)) {
        await updateSymptoms({ symptomId: symptom?.id, payloadSymptom });
      } else {
        await addSymptoms(payloadSymptom);
      }
      recordSymptomsEvents(trackEventInFlow, {
        EventName: symptom ? 'SymptomsEdited' : 'SymptomsAddCompleted',
        sy_entry_point: 'my_health_profile',
        sy_name: addedSymptom?.symptom?.value?.toString(),
        sy_start_date: dayjs(addedSymptom?.start_date).format('YYYY/MM/DD'),
        sy_end_date: dayjs(addedSymptom?.end_date).format('YYYY/MM/DD'),
        sy_record_added: !!addedSymptom?.external_reports?.length,
        sy_notes: addedSymptom?.notes,
        sy_status: addedSymptom?.clinicalStatus?.toString(),
      });
      toast({
        title: `Successfully ${isEditing ? 'edit' : 'add'}ed the symptom`,
        status: 'success',
        duration: 4000,
        isClosable: true,
      });
      closeDialog?.();
      if (!isEditing) {
        navigate(NavigationHelper.getEhrView(false, 'symptoms'));
      }
    } catch (_err) {
      toast({
        title: 'Something went wrong. Please try again.',
        status: 'error',
        duration: 4000,
        isClosable: true,
      });
    } finally {
      isLoading(false);
    }
  };

  const onOptionSelectAnalytic = useCallback((option: SelectOptionProps) => {
    if (option.value && !symptom) {
      recordSymptomsEvents(trackEventInFlow, {
        EventName: 'SymptomsAddInProgName',
        sy_entry_point: 'my_health_profile',
        sy_name: option.value.toString(),
      });
    }
  }, []);

  const datePickerChangeHandler = (date: Date | null, type: string) => {
    if (type === 'start') {
      if (dayjs(date).isValid()) {
        form.setValue('start_date', dayjs(date).format('YYYY-MM-DD'));
      } else {
        form.setValue('start_date', '');
      }
      startDatePickerPopover.onClose();
    } else {
      if (dayjs(date).isValid()) {
        form.setValue('end_date', dayjs(date).format('YYYY-MM-DD'));
      } else {
        form.setValue('end_date', '');
      }
      endDatePickerPopover.onClose();
    }
    if (!symptom) {
      recordSymptomsEvents(trackEventInFlow, {
        EventName: type === 'start' ? 'SymptomsAddInProgStartDate' : 'SymptomsAddInProgEndDate',
        ...(type === 'start'
          ? { sy_start_date: dayjs(date).format('YYYY/MM/DD') }
          : { sy_end_date: dayjs(date).format('YYYY/MM/DD') }),
        sy_entry_point: 'my_health_profile',
      });
    }
  };

  const datePickerClearHandler = (type: string) => {
    if (type === 'start') {
      form.setValue('start_date', '');
      startDatePickerPopover.onClose();
    } else {
      form.setValue('end_date', '');
      endDatePickerPopover.onClose();
    }
  };

  const removeAttachedMedicalRecordHandler = (record: AttachedMedicalRecord) => {
    form.setValue(
      'external_reports',
      externalReportsField.filter((item) => item.id !== record.id)
    );
  };
  useEffect(() => {
    const subscription = form.watch(() => {
      setIsFormValid(true);
    });
    Promise.all([medplumApi.valueSetList.getAllValueSetFromDirectus(false, FACT_CODE_SYSTEM.SEVERITY)]).then(
      ([data]) => {
        const concepts = data?.compose?.include[0]?.concept || data || [];
        setSeverityOption(
          concepts.map((e: { display: string; code: string }) => ({
            label: e.display,
            value: e.code,
          }))
        );
      }
    );
    return () => subscription.unsubscribe();
  }, []);
  return (
    <FormProvider {...form}>
      <Box
        color="black"
        display="flex"
        flexDirection="column"
        mt={6}
      >
        <Flex
          direction="column"
          gap="10"
        >
          <SymptomSelect
            symptomOptions={symptomList}
            onAfterSelect={onOptionSelectAnalytic}
            setShowCustomInput={setShowCustomInput}
          />
          {showCustomInput && (
            <FormControl
              isInvalid={!!form.formState.errors.custom_symptom}
              variant="floating"
            >
              <Input
                defaultValue={form.watch('custom_symptom') || ''}
                placeholder=" "
                {...form.register('custom_symptom')}
                onChange={(e) => {
                  form.setValue('custom_symptom', e?.target?.value);
                  form.trigger('custom_symptom');
                }}
                onBlurCapture={(e) => {
                  if (e.target.value && !symptom) {
                    recordSymptomsEvents(trackEventInFlow, {
                      EventName: 'SymptomsAddInProgName',
                      sy_entry_point: 'my_health_profile',
                      sy_name: e.target.value.toString(),
                    });
                  }
                }}
              />
              <FormLabel>Enter the symptom*</FormLabel>
              <FormErrorMessage>This field is required</FormErrorMessage>
            </FormControl>
          )}
          <Flex
            mb="0"
            gap="6px"
            direction="column"
          >
            <DatePickerField
              name="start_date"
              labelText="Date of diagnosis"
              errorText="This field is required"
              rules={{ required: true }}
              datePickerChangeHandler={(date) => datePickerChangeHandler(date, 'start')}
              datePickerClearHandler={() => datePickerClearHandler('start')}
              datePickerPopover={startDatePickerPopover}
              isClearDateButtonDisabled={startDateField?.length === 0}
              selected={dayjs(startDateField).isValid() ? dayjs(startDateField).toDate() : null}
              popoverProps={{ placement: 'bottom-start' }}
              maxDate={endDateField && dayjs(endDateField).isValid() ? dayjs(endDateField).toDate() : new Date()}
            />
            <Text
              fontSize="12px"
              color="charcoal.60"
              mt={1}
            >
              Don&apos;t remember the exact date? An estimate helps provide context to your medical history.
            </Text>
          </Flex>
          <FormControl
            mt="-5"
            variant="floating"
            isInvalid={!!form.formState.errors.symptoms_severity}
          >
            <SearchableSelect
              name="symptoms_severity"
              labelText="Select severity*"
              options={severityOption}
              value={form.watch('symptoms_severity')}
              onChange={(option: SelectOptionProps) => {
                form.setValue(
                  'symptoms_severity',
                  option ? { value: String(option.value), label: String(option.label) } : null
                );
                form.trigger('symptoms_severity');
              }}
              isClearable={false}
              style={{ textTransform: 'capitalize' }}
            />
            <FormErrorMessage>
              {form?.formState?.errors?.symptoms_severity?.value?.message ||
                form?.formState?.errors?.symptoms_severity?.label?.message}
            </FormErrorMessage>
          </FormControl>
          <Flex
            mb="0"
            alignItems="center"
            justifyContent="space-between"
            borderBottom="1px solid var(--chakra-colors-iris-500)"
            color="var(--chakra-colors-iris-500)"
          >
            <FormLabel
              fontSize="18px"
              fontWeight={400}
              mb="1"
            >
              Do you still have this symptom?
            </FormLabel>
            <Switch
              size="md"
              mb={2}
              isChecked={form.watch('clinicalStatus')}
              onChange={(e) => {
                form.setValue('clinicalStatus', e.target.checked);
                if (!symptom) {
                  recordSymptomsEvents(trackEventInFlow, {
                    EventName: 'SymptomsAddInProgStatus',
                    sy_entry_point: 'my_health_profile',
                    sy_name: e.target.value.toString(),
                    sy_status: e.target.checked,
                  });
                }
              }}
            />
          </Flex>
          {!form.watch('clinicalStatus') && (
            <Flex>
              <DatePickerField
                name="end_date"
                labelText="End date for this symptom"
                datePickerChangeHandler={(date) => datePickerChangeHandler(date, 'end')}
                datePickerClearHandler={() => datePickerClearHandler('end')}
                datePickerPopover={endDatePickerPopover}
                isClearDateButtonDisabled={endDateField?.length === 0}
                selected={dayjs(endDateField).isValid() ? dayjs(endDateField).toDate() : null}
                popoverProps={{ placement: 'bottom-start' }}
                minDate={
                  startDateField && dayjs(startDateField).isValid() ? dayjs(startDateField).toDate() : new Date()
                }
                maxDate={new Date()}
              />
            </Flex>
          )}
          <FormControl
            mb="0"
            variant="floating"
            isInvalid={!!form.formState.errors.notes}
          >
            <Input
              id="notes"
              name="notes"
              defaultValue={form.watch('notes') || ''}
              placeholder=" "
              onChange={(e) => {
                form.setValue('notes', e.target.value);
                form.trigger('notes');
              }}
              onBlurCapture={(e) => {
                if (!symptom) {
                  recordSymptomsEvents(trackEventInFlow, {
                    EventName: 'SymptomsAddInProgNotes',
                    sy_entry_point: 'my_health_profile',
                    sy_name: e.target.value.toString(),
                  });
                }
              }}
            />
            <FormLabel fontWeight="normal">Add your notes here</FormLabel>
          </FormControl>
          <Stack
            mb="0"
            spacing={2}
          >
            <MedicalRecordSelect
              labelText="Link health records"
              onSelectExtra={(value) => {
                if (!symptom) {
                  recordSymptomsEvents(trackEventInFlow, {
                    EventName: 'SymptomsAddInProgRecordsAdded',
                    sy_entry_point: 'my_health_profile',
                    sy_record_added: !!value?.length,
                  });
                }
              }}
            />
            {externalReportsField && externalReportsField?.length > 0 && (
              <LinkedDocumentsCard
                records={externalReportsField}
                onRemove={removeAttachedMedicalRecordHandler}
                showRemoveButton
              />
            )}
          </Stack>
        </Flex>
        <HStack
          justifyContent="flex-end"
          py="4"
          mt="8"
        >
          <Button
            isDisabled={!isValid || !isFormValid}
            isLoading={isSubmitting}
            onClick={handleSubmit(onSubmit)}
          >
            {isEditing ? 'Save' : 'Add'}
          </Button>
        </HStack>
      </Box>
    </FormProvider>
  );
}
