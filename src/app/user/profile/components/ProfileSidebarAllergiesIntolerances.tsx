import { Container, Flex, Spacer, Stack, useDisclosure, useTheme, useToast } from '@chakra-ui/react';
import React, { PropsWithChildren, Suspense, useEffect, useState } from 'react';
import { Edit3, Trash as TrashIcon } from 'react-feather';
import { recordAllergiesEvents } from '@user/lib/events-analytics-manager';
import { medplumApi } from '@user/lib/medplum-api';
import { useNavigate } from 'react-router-dom';
import { NavigationHelper, PATIENT_DEFINED, ROUTE_ACTIONS, ROUTE_VARIABLES } from '@user/lib/constants';
import { formatDateForDisplay } from '@utils/utils';
import dayjs from 'dayjs';

import { useAnalyticsService, useAuthService, usePublicSettings } from '@lib/state';
import { FormSkeleton } from '../../../../components/ui/Form';
import {
  MoreActionsMenu,
  MoreActionsMenuButton,
  MoreActionsMenuItem,
  MoreActionsMenuList,
} from '../../../../components/ui/Menu';
import ProfileSidebarAllergyForm from './ProfileSidebarAllergiesForm';
import { useAllergiesIntolerancesList } from '../../lib/medplum-state';
import {
  Card,
  CardCreatedAtLabel,
  CardHeading,
  SidebarAddButton,
  SidebarCloseButton,
  SidebarHeading,
  SidebarHelperTooltip,
} from './SidebarComponents';
import { MODAL_VARIANTS, Modal } from 'src/components/Modal';
import { ISidebarProps } from '@lib/models/misc';
import { SidebarEmptyState } from './SidebarEmptyState';
import { AllergyIntolerance } from 'src/gql/graphql';
import {
  ConsentModal,
  ConsentModalContent,
  ConsentModalFooter,
  ConsentModalHeading,
  ConsentModalPrimaryButton,
  ConsentModalSecondaryButton,
} from './ConsentModal';
import { FACT_CODE_SYSTEM, enumTaxonomyQuery } from '@lib/constants';
import { LinkedDocumentsCard, LinkedDocumentsLabel } from './LinkedDocumentsCard';
import { useExtractDocumentResource } from 'src/app/medical-records/lib/state';

interface Item {
  display: string;
  code: string;
}

const tooltipTextData = {
  WHAT_IS_AN_ALLERGY_TITLE: 'What is an allergy and/or intolerance?',
  WHAT_IS_AN_ALLERGY:
    "An allergy is an exaggerated immune response to a usually harmless substance, causing various symptoms ranging from mild discomfort to severe reactions. An intolerance is a non-immune adverse reaction to a particular substance, often involving digestive or metabolic issues, without the immune system's involvement.",
  FOOD_ALLERGY_TITLE: 'What is a food allergy and/or intolerance?',
  FOOD_ALLERGY_DESCRIPTION: `A food intolerance or allergy is a reaction to another substance you eat that may cause the same symptoms as a food allergy—such as difficulty breathing, facial swelling, hives and/or itching, nausea, vomiting, cramping, and diarrhoea. If you suspect a food allergy, your doctor may test you. The most frequent allergies include milk, eggs, peanuts, tree nuts, shellfish, wheat, soy, and sesame. \n Source: Mayo Clinic`,
  DRUG_ALLERGY_TITLE: 'What is a drug allergy and/or intolerance?',
  DRUG_ALLERGY_DESCRIPTION:
    'Any medicine— non-prescription, prescription, or herbal—can provoke a drug allergy. However, a drug allergy is more likely with certain medicines. The most common symptoms of drug allergy are hives, rash, or fever. Breathing difficulties, facial swelling, hives, itching, and severe respiratory failure are just a few of the symptoms that can indicate an allergic reaction to medicine. It is important that you record any medical (e.g., latex, sulfa medications) or drug (e.g., penicillin) allergies to ensure that they are not unintentionally administered during any emergency treatments or other clinical scenarios.\n Source: Mayo Clinic',
  ENVIRONMENT_ALLERGY_TITLE: 'What is an environmental allergy and/or intolerance?',
  ENVIRONMENT_ALLERGY_DESCRIPTION:
    'Environmental allergies occur when the immune system reacts to a foreign substance, such as pollen, smoke, odour, exhaust, colds, or infections. Symptoms include sneezing, coughing, itchy eyes, rashes on the skin, runny nose, and coughing. The severity of allergic reactions varies from individual to individual. A skin prick test is a quick test that an allergy specialist can do to find out if you have an allergy to any of the most common triggers. \n Source: Mayo Clinic',
  BILOGIC_ALLERGY_TITLE: 'What is a biologic allergy and/or intolerance?',
  BILOGIC_ALLERGY_DESCRIPTION:
    'A biological allergy, or simply an allergy, is an overreaction of the immune system to a substance (allergen) that is typically harmless, leading to symptoms like itching, sneezing, or hives',
};

function AllergiesCard({
  title,
  diagnosedDate,
  onEdit,
  onRemove,
  isPublicMode = false,
  lastOccurrenceDate,
  notes,
  Altype,
  isCustomEntry,
  evidence,
}: PropsWithChildren<{
  title: string;
  diagnosedDate: string;
  onEdit?: () => void;
  onRemove?: (close: () => void) => void;
  isPublicMode: boolean;
  lastOccurrenceDate?: string;
  notes: string;
  Altype: any;
  isCustomEntry?: boolean;
  evidence?: any[];
}>) {
  const deleteModal = useDisclosure();
  const theme = useTheme();
  const [loader, setLoader] = useState(false);
  const removeHandler = () => {
    setLoader(true);
    onRemove?.(deleteModal.onClose);
    setTimeout(() => setLoader(false), 5000);
  };
  return (
    <>
      <ConsentModal {...deleteModal}>
        <ConsentModalHeading>
          Are you sure you want
          <br />
          to remove this entry?
        </ConsentModalHeading>
        <ConsentModalContent>This cannot be undone.</ConsentModalContent>
        <ConsentModalFooter>
          <ConsentModalSecondaryButton
            variant="quietDanger"
            color="red.100"
            onClick={removeHandler}
            isLoading={loader}
          >
            Remove
          </ConsentModalSecondaryButton>
          <ConsentModalPrimaryButton onClick={deleteModal.onClose}>Cancel</ConsentModalPrimaryButton>
        </ConsentModalFooter>
      </ConsentModal>
      <Card mt="2">
        <Stack>
          <Flex justifyContent="space-between">
            <CardHeading maxWidth="90%">
              {title} {isCustomEntry && '[Custom entry]'}
            </CardHeading>
            {!isPublicMode && (
              <MoreActionsMenu>
                <MoreActionsMenuButton />
                <MoreActionsMenuList>
                  <MoreActionsMenuItem
                    icon={<Edit3 size={16} />}
                    onClick={onEdit}
                  >
                    Edit
                  </MoreActionsMenuItem>
                  <MoreActionsMenuItem
                    icon={<TrashIcon size={16} />}
                    onClick={() => deleteModal.onOpen()}
                  >
                    Delete
                  </MoreActionsMenuItem>
                </MoreActionsMenuList>
              </MoreActionsMenu>
            )}
          </Flex>
          {diagnosedDate ? (
            <CardCreatedAtLabel
              p="2px 6px"
              w="max-content"
              color="#FFFFFF"
              bg={theme.colors.iris[500]}
              borderRadius="base"
              fontSize="base"
              mt={0}
            >
              {formatDateForDisplay(diagnosedDate)}
              {dayjs(lastOccurrenceDate).isValid() ? ` - ${formatDateForDisplay(lastOccurrenceDate)}` : ' - Present'}
            </CardCreatedAtLabel>
          ) : (
            false
          )}
          {Altype?.length && (
            <CardCreatedAtLabel
              p="2px 6px"
              w="max-content"
              color="#000"
              bg="#FFC1AD"
              borderRadius="base"
              fontSize="base"
              mt={0}
              textTransform="capitalize"
            >
              {Altype.join(', ')}
            </CardCreatedAtLabel>
          )}
          {notes && (
            <CardCreatedAtLabel
              pt={3}
              color="#686A6C"
            >
              {notes}
            </CardCreatedAtLabel>
          )}
          {(evidence?.length ?? 0) > 0 && evidence?.[0]?.valueReference?.resource && (
            <Flex
              direction="column"
              gap="2px"
            >
              <LinkedDocumentsLabel />
              <LinkedDocumentsCard records={useExtractDocumentResource(evidence)} />
            </Flex>
          )}
        </Stack>
      </Card>
    </>
  );
}

export default function ProfileSidebarAllergiesIntolerances({ onClose, name, action }: ISidebarProps) {
  const toast = useToast();
  const navigate = useNavigate();
  const { PROFILE, EHR, ALLERGIES } = ROUTE_VARIABLES;
  const { ADD } = ROUTE_ACTIONS;
  const { isPublicMode } = usePublicSettings();

  const [selectedAllergy, setSelectedAllergy] = React.useState<AllergyIntolerance | null>(null);
  const allergyModal = useDisclosure();
  const { authenticatedUser } = useAuthService();
  const sharePatientId = localStorage.getItem('sharePatientId');
  const { allergyListOfData, deleteAllergyIntolerance } = useAllergiesIntolerancesList(
    isPublicMode ? sharePatientId : `Patient/${authenticatedUser?.id}`
  );
  const { trackEventInFlow } = useAnalyticsService();
  const [allergyIntoleranceList, setAllergyIntoleranceList] = useState<{
    allergyOption: { label: string; value: string }[];
    categoryOption: { label: string; value: string }[];
    statusOption: { label: string; value: string }[];
    criticalityOption: { label: string; value: string }[];
  }>();
  const [submitting, setSubmitting]: any = useState<boolean>();

  const onAddHandler = () => {
    setSelectedAllergy(null);
    allergyModal.onOpen();
    recordAllergiesEvents(trackEventInFlow, {
      EventName: 'AllergiesAddStarted',
      al_entry_point: 'my_health_profile',
    });
    navigate(`/${PROFILE}/${EHR}/${ALLERGIES}/${ADD}`);
  };

  useEffect(() => {
    if (isPublicMode) return;
    const mapOptions = (items: Item[]) => items?.map(({ display, code }) => ({ label: display, value: code })) || [];
    const fetchData = async () => {
      try {
        const systems = [FACT_CODE_SYSTEM.AllergyIntolerance, FACT_CODE_SYSTEM.Allergies_Intolerances_STATUS] as const;
        const results = await Promise.all([
          ...systems.map((system) => medplumApi.valueSetList.getAllValueSetFromDirectus(isPublicMode, system)),
          medplumApi.valueSetList.getAllValueSetHL7(
            isPublicMode,
            enumTaxonomyQuery.ALLERGIES_AND_OR_INTOLERANCES_CRITICALITY
          ),
          medplumApi.valueSetList.getAllValueSetHL7(
            isPublicMode,
            enumTaxonomyQuery.ALLERGIES_AND_OR_INTOLERANCES_CATEGORY
          ),
        ]);
        setAllergyIntoleranceList({
          allergyOption: mapOptions(results[0]),
          statusOption: mapOptions(results[1]),
          criticalityOption: mapOptions(results[2]),
          categoryOption: mapOptions(results[3]),
        });
      } catch (error) {
        console.error(error);
      }
    };
    fetchData();
  }, [isPublicMode]);

  const onEditHandler = (allergy: AllergyIntolerance) => {
    setSelectedAllergy(allergy);
    allergyModal.onOpen();
    recordAllergiesEvents(trackEventInFlow, {
      EventName: 'AllergiesInteracted',
    });
  };

  const onRemoveHandler = async (allergy: AllergyIntolerance, close: () => void) => {
    setSubmitting(true);
    const deleteTask = 'Delete Allergy / Intolerance';
    const identifier = 'urn:fh-workflow:task:delete:allergy-intolerance';
    const payload: any = {
      symptomId: allergy.id,
      deleteTask,
      identifier,
    };
    try {
      setSelectedAllergy(allergy);
      deleteAllergyIntolerance(payload).then((res) => {
        if (res) {
          toast({
            title: 'Allergy/Intolerance removed',
            status: 'success',
            duration: 3000,
            isClosable: true,
          });
          recordAllergiesEvents(trackEventInFlow, {
            EventName: 'AllergiesRemoved',
            al_name: allergy?.code?.coding?.[0]?.code ?? '',
          });
          close();
        }
      });
    } catch (e) {
      toast({
        title: 'Error',
        description: 'An error occurred while removing the allergy/intolerance',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    } finally {
      setSubmitting(false);
    }
  };
  useEffect(() => {
    if (action === ADD) {
      allergyModal.onOpen();
    }
  }, [action]);

  const closeFn = () => {
    allergyModal.onClose();
    if (action === ADD) navigate(NavigationHelper.getEhrView(false, 'allergies'));
  };

  const isLoading = (incoming: boolean) => {
    setSubmitting(incoming);
  };

  return (
    <>
      <Modal
        title="Allergies and/or Intolerances"
        showModalFooter={false}
        isCentered
        variant={MODAL_VARIANTS.PERIWINKLE}
        {...allergyModal}
        onClose={closeFn}
      >
        <Suspense fallback={<FormSkeleton />}>
          {submitting ? (
            <FormSkeleton />
          ) : (
            <ProfileSidebarAllergyForm
              allergies={selectedAllergy}
              allergy_name={selectedAllergy}
              name={name || ''}
              allergyOptions={allergyIntoleranceList?.allergyOption || []}
              allergyCriticalities={allergyIntoleranceList?.criticalityOption || []}
              allergyTypes={allergyIntoleranceList?.categoryOption || []}
              closeDialog={closeFn}
              isLoading={isLoading}
            />
          )}
        </Suspense>
      </Modal>
      <Container
        position="relative"
        height="full"
        overflowY="scroll"
        overflowX="hidden"
        className="hide-scrollbar"
      >
        <Stack
          py="4"
          height="full"
        >
          <Flex justifyContent="space-between">
            <SidebarHeading>Allergies and/or Intolerances</SidebarHeading>
            <SidebarCloseButton onClick={onClose} />
          </Flex>
          {allergyListOfData.length === 0 && (
            <SidebarEmptyState
              actionButtonText="Add"
              title="Update allergy and/or intolerance details"
              imageSrc="/empty-card-allergies.png"
              completeInfoText={isPublicMode ? undefined : '+10% to complete your profile'}
              onClick={onAddHandler}
              {...(isPublicMode ? { hideActionButton: true } : { onClick: onAddHandler })}
              isPublicMode={isPublicMode}
            />
          )}
          {allergyListOfData.length > 0 && (
            <>
              {!isPublicMode && <SidebarAddButton onClick={onAddHandler}>Add</SidebarAddButton>}
              <Stack
                mt={0}
                gap="2"
                overflowY="scroll"
                height="full"
                className="hide-scrollbar"
              >
                {allergyListOfData.map((allergy: AllergyIntolerance) => (
                  <AllergiesCard
                    key={allergy?.id}
                    title={allergy?.code?.coding?.[0]?.display ?? ''}
                    diagnosedDate={allergy?.onsetDateTime ?? ''}
                    lastOccurrenceDate={allergy?.lastOccurrence ?? ''}
                    onEdit={() => onEditHandler(allergy)}
                    onRemove={(close: () => void) => onRemoveHandler(allergy, close)}
                    isPublicMode={isPublicMode}
                    notes={allergy?.note?.[0]?.text ?? ''}
                    Altype={allergy?.category}
                    isCustomEntry={allergy.code?.coding?.[0]?.code === `al:${PATIENT_DEFINED}`}
                    evidence={allergy?.extension ?? []}
                  />
                ))}
              </Stack>
              <Spacer />
            </>
          )}
          <SidebarHelperTooltip
            text={tooltipTextData.WHAT_IS_AN_ALLERGY_TITLE}
            tooltipText={tooltipTextData.WHAT_IS_AN_ALLERGY}
          />
          ,
        </Stack>
      </Container>
    </>
  );
}
