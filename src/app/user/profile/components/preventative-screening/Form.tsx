import { FormProvider, useForm } from 'react-hook-form';
import {
  Box,
  Button,
  FormControl,
  FormLabel,
  HStack,
  Input,
  Stack,
  Text,
  useDisclosure,
  useTheme,
  useToast,
} from '@chakra-ui/react';
import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';
import { ChevronLeftIcon } from '@chakra-ui/icons';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { usePreventativeScreening } from '@user/lib/medplum-state';
import { DOCUMENT_REF, NavigationHelper, preventativeScreeningConstants } from '@user/lib/constants';
import { useNavigate } from 'react-router-dom';
import { recordPreventativeScreeningEvents } from '@user/lib/events-analytics-manager';

import { MasterScreening } from '@lib/models/screening';
import { DatePickerField } from '../../../../../components/ui/Form';
import { LinkedDocumentsCard } from '../LinkedDocumentsCard';
import { useAnalyticsService, useAuthService } from '@lib/state';
import { AttachedMedicalRecord } from '@lib/models/medical-record';
import { YesOrNoAnswer } from '@lib/models/misc';
import { AnalyticsEventName, AnalyticsFlow, EventPropsNames } from '@lib/analyticsService';
import { MedicalRecordSelect } from '../MedicalRecordSelect';
import { Procedure } from 'src/gql/graphql';
import { ProcedureInput, createFhirResource } from './PreventativeScreeningHelper';
import { isEmptyObject } from '@lib/utils/utils';
import { useExtractDocumentResource } from 'src/app/medical-records/lib/state';

function getInitialFormData(
  screening: any,
  masterLinkId: any,
  familyHistory: YesOrNoAnswer | null,
  masterScreeningType: MasterScreening
) {
  return {
    id: screening?.id ?? '',
    screening_date: screening?.performedDateTime ? screening?.performedDateTime : '',
    screening: {
      master_id: masterScreeningType?.name,
    },
    focus_area: masterScreeningType.value,
    family_history: familyHistory ?? null,
    external_reports: screening ? useExtractDocumentResource(screening?.report) : [],
    notes: screening?.note?.[0]?.text ?? null,
  };
}

export function Form({
  screening,
  masterLinkId,
  masterScreeningType,
  familyHistory,
  onClose,
  hasPrevious,
  onPrevious,
  hasExternalReports = true,
}: {
  masterScreeningName?: string;
  masterLinkId: any;
  screening: Procedure | null;
  masterScreeningType: MasterScreening;
  familyHistory: any;
  onClose: () => void;
  hasPrevious: boolean;
  onPrevious: () => void;
  hasExternalReports?: boolean;
}) {
  const theme = useTheme();
  const toast = useToast();
  const navigate = useNavigate();
  const datePickerPopover = useDisclosure();

  const { authenticatedUser } = useAuthService();
  const { addPreventativeScreening, updatePreventativeScreening } = usePreventativeScreening(authenticatedUser?.id);
  const [isFormValid, setIsFormValid] = useState(false);
  const [hasTrackedInteraction, setHasTrackedInteraction] = useState(false);

  const { trackEventInFlow } = useAnalyticsService();

  // Track interaction event only once when form is loaded with existing screening
  useEffect(() => {
    if (screening?.id && !hasTrackedInteraction) {
      recordPreventativeScreeningEvents(trackEventInFlow, {
        EventName: 'PreventativeScreeningInteracted',
        ps_entry_points: 'my_health_profile',
        ps_type: masterScreeningType?.value,
      });
      setHasTrackedInteraction(true);
    }
  }, [screening?.id, hasTrackedInteraction, trackEventInFlow, masterScreeningType?.value]);

  const form = useForm({
    mode: 'onChange',
    resolver: zodResolver(
      z.object({
        screening_date: z.string(),
        screening: z.object({
          master_id: z.string(),
        }),
        focus_area: z.string(),
        family_history: z.any(),
        notes: z.any(),
        external_reports: z.array(z.any()),
      })
    ),
    defaultValues: getInitialFormData(screening, masterLinkId, familyHistory, masterScreeningType),
  });
  const {
    handleSubmit,
    formState: { isSubmitting },
  } = form;
  const dateField = form.watch('screening_date');
  const externalReportsField = form.watch('external_reports');

  const datePickerChangeHandler = (date: Date | null) => {
    if (dayjs(date).isValid()) {
      form.setValue('screening_date', dayjs(date).format('YYYY-MM-DD'));
    } else {
      form.setValue('screening_date', '');
    }
    recordPreventativeScreeningEvents(trackEventInFlow, {
      EventName: 'PreventativeScreeningAddInProgDate',
      ps_entry_points: 'my_health_profile',
      ps_type: masterScreeningType.value,
      ps_date: dayjs(date).format('YYYY/MM/DD'),
    });
    datePickerPopover.onClose();
  };

  const datePickerClearHandler = () => {
    form.setValue('screening_date', '');
    datePickerPopover.onClose();
    trackEventInFlow(AnalyticsFlow.PreventativeScreeningManaged, AnalyticsEventName.PreventativeScreeningManaged, {
      [EventPropsNames.Action]: screening ? 'Edit' : 'Add',
      [EventPropsNames.ScreenName]: hasPrevious ? 'Story card' : `${masterScreeningType.value} screen`,
      [EventPropsNames.RecordSearched]: !!externalReportsField?.length,
      [EventPropsNames.LinkedRecordCount]: externalReportsField?.length,
      [EventPropsNames.CompletedSuccess]: true,
      [EventPropsNames.ScreeningType]: masterScreeningType.value,
      [EventPropsNames.RecordsLinked]: externalReportsField?.length,
    });
  };

  async function onSubmit(preventativeScreening: any) {
    try {
      if (!authenticatedUser?.id) return;
      const patientId = authenticatedUser.id;
      const matchingScreen = preventativeScreeningConstants.find((screen) =>
        Object.values(screen).some(
          (item: any) => item.type?.toLowerCase() === preventativeScreening.focus_area.toLowerCase()
        )
      );

      if (!matchingScreen) return;

      const [, value] = Object.entries(matchingScreen)[0];
      const reportReferences =
        preventativeScreening?.external_reports
          ?.filter((report: any) => report.id !== null && report.id !== undefined)
          .map((report: any) => `${DOCUMENT_REF}/${report.id}`) || [];

      const procedureInput: ProcedureInput = {
        identifier: value.identifier,
        status: 'completed',
        code: value.snowMed,
        performedDateTime: preventativeScreening.screening_date,
        category: value.category,
        subject: `Patient/${patientId}`,
        recorder: `Patient/${patientId}`,
        report: reportReferences,
        note: preventativeScreening.notes,
      };

      const payload = createFhirResource('Procedure', procedureInput);

      let action: 'Add' | 'Edit' = 'Add';

      if (isEmptyObject(screening)) {
        await addPreventativeScreening(payload);
      } else {
        const procedureId = screening?.id ?? '';
        if (!procedureId) return;

        const patchPayload: Array<{ op: string; path: string; value: any }> = [];

        const addPatch = (path: string, newValue: any, currentValue: any) => {
          if (newValue && !currentValue) {
            patchPayload.push({ op: 'add', path, value: newValue });
          } else if (Array.isArray(newValue) && newValue?.[0]?.text === '') {
            patchPayload.push({ op: 'remove', path, value: '' });
          } else if (newValue !== undefined && newValue !== currentValue) {
            patchPayload.push({ op: 'replace', path, value: newValue });
          } else if (newValue === undefined && currentValue !== undefined) {
            patchPayload.push({ op: 'remove', path, value: '' });
          }
        };

        const updatedReports = reportReferences.map((val: any) => {
          return {
            reference: val,
          };
        });
        addPatch('/performedDateTime', preventativeScreening?.screening_date, screening?.performedDateTime);
        addPatch('/report', updatedReports, screening?.report);
        addPatch('/note', preventativeScreening?.notes, screening?.note);

        if (patchPayload.length > 0) {
          await updatePreventativeScreening({ procedureId, payload: patchPayload });
          action = 'Edit';
        }
      }

      recordPreventativeScreeningEvents(trackEventInFlow, {
        EventName: isEmptyObject(screening) ? 'PreventativeScreeningAddCompleted' : 'PreventativeScreeningEdited',
        ps_entry_points: 'my_health_profile',
        ps_type: masterScreeningType.value,
        ps_date: dayjs(dateField).format('YYYY/MM/DD'),
        ps_notes: preventativeScreening.notes,
        ps_records_added: !!reportReferences?.length,
        ps_family_history: false,
      });
      toast({
        title: `Screening ${action.toLowerCase()}ed`,
        description: `Your screening has been ${action.toLowerCase()}ed successfully.`,
        status: 'success',
        duration: 4000,
        isClosable: true,
      });

      onClose();
    } catch (_) {
      navigate(NavigationHelper.getEhrView(false, 'preventative-screening'));
      toast({
        title: 'Something went wrong. Please try again.',
        status: 'error',
        duration: 4000,
        isClosable: true,
      });
    }
  }

  const removeAttachedMedicalRecordHandler = (record: AttachedMedicalRecord) => {
    form.setValue(
      'external_reports',
      externalReportsField.filter((item: any) => item.id !== record.id)
    );
  };
  useEffect(() => {
    const subscription = form.watch(() => {
      setIsFormValid(true);
    });

    return () => subscription.unsubscribe();
  }, [form]);
  return (
    <FormProvider {...form}>
      <Box
        color="black"
        display="flex"
        flexDirection="column"
        mt="6"
      >
        <Box mt="3">
          <DatePickerField
            name="screening_date"
            labelText="Date of test*"
            datePickerChangeHandler={datePickerChangeHandler}
            datePickerClearHandler={datePickerClearHandler}
            datePickerPopover={datePickerPopover}
            isClearDateButtonDisabled={dateField?.length === 0}
            selected={dayjs(dateField).isValid() ? dayjs(dateField).toDate() : null}
            maxDate={new Date()}
          />
        </Box>
        <FormControl
          variant="floating"
          mt="8"
          isInvalid={!!form.formState.errors.notes}
        >
          <Input
            id="notes"
            name="notes"
            defaultValue={form.watch('notes') || ''}
            placeholder=" "
            onChange={(e) => {
              form.setValue('notes', e.target.value);
              form.trigger('notes');
            }}
            onBlurCapture={(e) =>
              recordPreventativeScreeningEvents(trackEventInFlow, {
                EventName: 'PreventativeScreeningAddInProgNotes',
                ps_entry_points: 'my_health_profile',
                ps_type: masterScreeningType.value,
                ps_notes: String(e?.target?.value),
              })
            }
          />
          <FormLabel fontWeight="normal">Add your notes here</FormLabel>
        </FormControl>
        {hasExternalReports ? (
          <Stack
            spacing={2}
            mt="10"
          >
            <MedicalRecordSelect />
            {externalReportsField?.filter((record) => record && Object.values(record).some((value) => value !== null))
              .length > 0 && (
              <LinkedDocumentsCard
                records={externalReportsField.filter(
                  (record) => record && Object.values(record).some((value) => value !== null)
                )}
                onRemove={removeAttachedMedicalRecordHandler}
                showRemoveButton
              />
            )}
          </Stack>
        ) : null}
      </Box>
      <HStack
        justifyContent="space-between"
        flexDirection={hasPrevious ? 'row' : 'row-reverse'}
        mt="8"
        mb="4"
      >
        {hasPrevious && (
          <Button
            leftIcon={
              <ChevronLeftIcon
                color={theme.colors.royalBlue[500]}
                width="20px"
                height="20px"
              />
            }
            variant="quiet"
            onClick={onPrevious}
          >
            <Text
              fontSize="md"
              color="royalBlue.500"
            >
              Previous
            </Text>
          </Button>
        )}
        <Button
          isDisabled={!(dateField?.length > 1 || externalReportsField?.length) || !isFormValid}
          isLoading={isSubmitting}
          onClick={handleSubmit(onSubmit)}
        >
          {!screening ? 'Add' : 'Save'}
        </Button>
      </HStack>
    </FormProvider>
  );
}
