import React, { PropsWithChildren } from 'react';
import { Flex, Spacer, Stack, useDisclosure } from '@chakra-ui/react';
import { Edit3, Trash as TrashIcon } from 'react-feather';
import { formatDateForDisplay } from '@utils/utils';

import { Card, CardHeading, CardPerformedLabel } from '../SidebarComponents';
import {
  MoreActionsMenu,
  MoreActionsMenuButton,
  MoreActionsMenuItem,
  MoreActionsMenuList,
} from '../../../../../components/ui/Menu';
import {
  ConsentModal,
  ConsentModalContent,
  ConsentModalFooter,
  ConsentModalHeading,
  ConsentModalPrimaryButton,
  ConsentModalSecondaryButton,
} from '../ConsentModal';

export function ScreeningCard({
  title,
  children,
  createdAt,
  onRemove,
  onEdit,
  isPublicMode = false,
  isLoading,
}: PropsWithChildren<{
  title: string;
  createdAt: string;
  masterLinkId?: any;
  onRemove: () => void;
  onEdit: () => void;
  isPublicMode?: boolean;
  isLoading?: boolean;
}>) {
  const deleteModal = useDisclosure();
  return (
    <Card>
      <Stack>
        <Flex
          justifyContent="space-between"
          color="fluentHealthText.100"
        >
          <CardHeading>{title}</CardHeading>
          {!isPublicMode && (
            <MoreActionsMenu>
              <MoreActionsMenuButton />
              <MoreActionsMenuList>
                <MoreActionsMenuItem
                  icon={<Edit3 size={16} />}
                  onClick={onEdit}
                >
                  Edit
                </MoreActionsMenuItem>
                <MoreActionsMenuItem
                  icon={<TrashIcon size={16} />}
                  onClick={() => deleteModal.onOpen()}
                >
                  Delete
                </MoreActionsMenuItem>
              </MoreActionsMenuList>
            </MoreActionsMenu>
          )}
        </Flex>
        {createdAt && <CardPerformedLabel mt="-14px">{formatDateForDisplay(createdAt)}</CardPerformedLabel>}
        <Spacer />
        {children}
      </Stack>
      <ConsentModal {...deleteModal}>
        <ConsentModalHeading>
          Are you sure you want
          <br />
          to remove this entry?
        </ConsentModalHeading>
        <ConsentModalContent>This cannot be undone.</ConsentModalContent>
        <ConsentModalFooter>
          <ConsentModalSecondaryButton
            variant="quietDanger"
            color="red.100"
            onClick={() => {
              onRemove();
              deleteModal.onClose();
            }}
            isLoading={isLoading}
          >
            Remove
          </ConsentModalSecondaryButton>
          <ConsentModalPrimaryButton onClick={deleteModal.onClose}>Cancel</ConsentModalPrimaryButton>
        </ConsentModalFooter>
      </ConsentModal>
    </Card>
  );
}
