import { FormProvider, useForm } from 'react-hook-form';
import {
  Box,
  Button,
  FormControl,
  FormErrorMessage,
  HStack,
  Stack,
  Text,
  useDisclosure,
  useTheme,
  useToast,
} from '@chakra-ui/react';
import dayjs from 'dayjs';
import { ChevronLeftIcon } from '@chakra-ui/icons';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useImmunization } from '@user/lib/medplum-state';
import { useNavigate } from 'react-router-dom';
import { NavigationHelper } from '@user/lib/constants';
import { medplumApi } from '@user/lib/medplum-api';
import { useEffect, useState } from 'react';
import { recordVaccineEvents } from '@user/lib/events-analytics-manager';

import { Select } from 'src/components/ui/Select';
import { MasterScreening } from '@lib/models/screening';
import { DatePickerField } from '../../../../../components/ui/Form';
import { LinkedDocumentsCard } from '../LinkedDocumentsCard';
import { useAnalyticsService, useAuthService } from '@lib/state';
import { AttachedMedicalRecord } from '@lib/models/medical-record';
import { MedicalRecordSelect } from '../MedicalRecordSelect';
import { QuestionnaireResponse } from 'src/gql/graphql';
import { FHIR_CODE_SYSTEM_FACT_URL } from '@lib/constants';
import {
  FHIR_HL7_CODE_SYSTEM_IMMUNIZATION_ORIGIN,
  FHIR_HL7_STRUCTURE_DEFINITION_IMMUNIZATION_SUPPORTING_INFO,
} from 'src/constants/medplumConstants';
import { useExtractDocumentResource } from 'src/app/medical-records/lib/state';
import { formatDateForSave } from '@lib/utils/utils';

function getInitialFormData(screening: any, masterScreeningType: MasterScreening) {
  const dose = screening?.protocolApplied?.[0]?.doseNumberPositiveInt;
  const screening_date =
    screening?.occurrenceDateTime && dayjs(screening.occurrenceDateTime).isValid() ? screening.occurrenceDateTime : '';
  const external_reports = screening?.extension ? useExtractDocumentResource(screening.extension) : [];
  return {
    id: screening?.id ?? '',
    dose,
    screening_date,
    screening: { master_id: masterScreeningType.code },
    external_reports,
  };
}
function NoOptionsMessage() {
  return <Text>No additional dose options available.</Text>;
}
export function Form({
  screening,
  masterScreeningType,
  onClose,
  hasPrevious,
  onPrevious,
  hasExternalReports = true,
  link,
}: {
  masterScreeningName?: string;
  screening: QuestionnaireResponse | null;
  masterScreeningType: MasterScreening;
  onClose: () => void;
  hasPrevious: boolean;
  onPrevious: () => void;
  hasExternalReports?: boolean;
  filteredTrimesterOdPregnancyOption?: any;
  filteredAreYouPregnantOption?: any;
  link?: string;
}) {
  const theme = useTheme();
  const toast = useToast();
  const navigate = useNavigate();
  const datePickerPopover = useDisclosure();
  const [isFormValid, setIsFormValid] = useState(false);
  const { trackEventInFlow } = useAnalyticsService();

  const { authenticatedUser } = useAuthService();
  const { addImmunization, updateImmunization, immunizationList } = useImmunization(authenticatedUser?.id);
  const defaultFormValues = getInitialFormData(screening, masterScreeningType);

  const form: any = useForm({
    mode: 'onChange',
    resolver: zodResolver(
      z.object({
        id: z.string(),
        dose: z.number(),
        screening_date: z.string().min(1, 'Date of vaccine is required'),
        external_reports: z.array(z.any()),
      })
    ),
    defaultValues: defaultFormValues,
  });
  const {
    handleSubmit,
    formState: { isSubmitting },
  } = form;
  const dateField = form.watch('screening_date');
  const externalReportsField = form.watch('external_reports');

  const datePickerChangeHandler = (date: Date | null) => {
    if (dayjs(date).isValid()) {
      form.setValue('screening_date', dayjs(date).format('YYYY-MM-DD'));
    } else {
      form.setValue('screening_date', '');
    }
    if (!screening) {
      recordVaccineEvents(trackEventInFlow, {
        EventName: 'VaccineAddInProgDate',
        vc_entry_point: 'my_health_profile',
        vc_date: dayjs(date).format('YYYY/MM/DD'),
        vc_type: masterScreeningType?.display,
      });
    }

    datePickerPopover.onClose();
  };
  const datePickerClearHandler = () => {
    form.setValue('screening_date', '');
    datePickerPopover.onClose();
  };

  async function onSubmit(vaccineForm: any) {
    try {
      const coding = await medplumApi.valueSetList.getFHIRCodingFromCMS(masterScreeningType?.code);
      const immunizationPayload: any = {
        resourceType: 'Immunization',
        // meta: { profile: [FHIR_HL7_STRUCTURE_DEFINITION_IMMUNIZATION] },
        identifier: [
          {
            system: FHIR_CODE_SYSTEM_FACT_URL,
            value: masterScreeningType?.code,
          },
        ],
        vaccineCode: { coding },
        status: vaccineForm.status || 'completed',
        occurrenceDateTime: formatDateForSave(vaccineForm.screening_date),
        patient: { reference: `Patient/${authenticatedUser?.id}` },
        reportOrigin: {
          coding: [
            {
              system: FHIR_HL7_CODE_SYSTEM_IMMUNIZATION_ORIGIN,
              code: 'recall',
              display: 'Patient Recall',
            },
          ],
        },
        protocolApplied: [
          {
            doseNumberPositiveInt: vaccineForm.dose,
          },
        ],
      };

      if (vaccineForm?.encounterId) {
        immunizationPayload.encounter = { reference: `Encounter/${vaccineForm.encounterId}` };
      }

      if (vaccineForm?.notes) {
        immunizationPayload.note = vaccineForm.notes;
      }
      if (vaccineForm.external_reports?.length) {
        immunizationPayload.extension = vaccineForm.external_reports.map((item: any) => ({
          url: FHIR_HL7_STRUCTURE_DEFINITION_IMMUNIZATION_SUPPORTING_INFO,
          valueReference: {
            reference: `DocumentReference/${item.id}`,
          },
        }));
      }
      const payload: any = vaccineForm.id
        ? { immunizationPayload: { ...immunizationPayload, id: vaccineForm.id }, immunizationId: vaccineForm.id }
        : { immunization: immunizationPayload };

      if (vaccineForm.id) {
        await updateImmunization(payload);
      } else {
        await addImmunization(payload);
      }
      recordVaccineEvents(trackEventInFlow, {
        EventName: vaccineForm.id ? 'VaccineEdited' : 'VaccineAddCompleted',
        vc_entry_point: 'my_health_profile',
        vc_date: dayjs(vaccineForm.screening_date).format('YYYY/MM/DD'),
        vc_dose_count: Number(vaccineForm.dose),
        vc_fully_vaccinated: !!vaccineForm.id,
        vc_records_added: !!vaccineForm.external_reports?.length,
        vc_type: masterScreeningType?.display,
      });
      toast({
        title: `Vaccine ${vaccineForm.screening ? 'edited' : 'added'}`,
        description: `Your vaccine has been ${vaccineForm.screening ? 'edited' : 'added'}`,
        status: 'success',
        duration: 4000,
        isClosable: true,
      });

      onClose();
      navigate(NavigationHelper.getEhrView(false, 'vaccines', link));
    } catch (_) {
      toast({
        title: 'Something went wrong. Please try again.',
        status: 'error',
        duration: 4000,
        isClosable: true,
      });
    }
  }

  const removeAttachedMedicalRecordHandler = (record: AttachedMedicalRecord) => {
    form.setValue(
      'external_reports',
      externalReportsField.filter((item: any) => item.id !== record.id)
    );
  };
  const currentImmunizationList =
    immunizationList?.filter((item: any) => item.identifier?.[0]?.value === masterScreeningType.code) || [];

  const usedDoses = currentImmunizationList.map((item: any) => item.protocolApplied?.[0]?.doseNumberPositiveInt);

  const allDoseOptions = Array.from({ length: 10 }, (_, i) => ({
    label: `Dose ${i + 1}`,
    value: String(i + 1),
  }));

  const availableDoseOptions = allDoseOptions.filter((option) => !usedDoses.includes(Number(option.value)));

  const defaultDoseOption = defaultFormValues?.dose
    ? { label: `Dose ${defaultFormValues.dose}`, value: Number(defaultFormValues.dose) }
    : availableDoseOptions.find((option) => option.value === String(defaultFormValues?.dose)) ||
      availableDoseOptions[0];

  useEffect(() => {
    if (defaultDoseOption) {
      form.setValue('dose', Number(defaultDoseOption.value));
    }
    const subscription = form.watch(() => {
      setIsFormValid(true);
    });
    return () => subscription.unsubscribe();
  }, []);
  return (
    <FormProvider {...form}>
      <form onSubmit={handleSubmit(onSubmit)}>
        <Box
          color="black"
          display="flex"
          flexDirection="column"
          gap={6}
          mt={10}
        >
          <Box>
            <FormControl isInvalid={!!form.formState.errors.dose}>
              <Select
                name="dose"
                labelText="Select vaccine dose*"
                defaultValue={defaultDoseOption}
                noOptionsMessage={NoOptionsMessage}
                onChange={(option: any) => {
                  if (!option) {
                    form.setValue('dose', '');
                    form.trigger('dose');
                    return;
                  }
                  form.setValue('dose', Number(option.value) || 1);
                  form.trigger('dose');
                  if (!screening) {
                    recordVaccineEvents(trackEventInFlow, {
                      EventName: 'VaccineAddInProgDoseCount',
                      vc_entry_point: 'my_health_profile',
                      vc_dose_count: Number(option.value),
                      vc_type: masterScreeningType?.display,
                    });
                  }
                }}
                options={availableDoseOptions}
                isSearchable
              />
              <FormErrorMessage>{form.formState.errors.dose?.message}</FormErrorMessage>
            </FormControl>
          </Box>
          <Box mt={6}>
            <DatePickerField
              name="screening_date"
              labelText="Date of vaccine*"
              datePickerChangeHandler={datePickerChangeHandler}
              datePickerClearHandler={datePickerClearHandler}
              datePickerPopover={datePickerPopover}
              isClearDateButtonDisabled={dateField?.length === 0}
              selected={dayjs(dateField).isValid() ? dayjs(dateField).toDate() : null}
              maxDate={new Date()}
            />
            <Text
              fontSize="12px"
              color="charcoal.60"
              mt={1}
            >
              Don&apos;t remember the exact date? An estimate helps provide context to your medical history.
            </Text>
            <FormErrorMessage>{form.formState.errors.screening_date?.message}</FormErrorMessage>
          </Box>
          {hasExternalReports && (
            <Stack spacing={2}>
              <MedicalRecordSelect />
              {externalReportsField.length > 0 && (
                <LinkedDocumentsCard
                  records={externalReportsField}
                  onRemove={removeAttachedMedicalRecordHandler}
                  showRemoveButton
                />
              )}
            </Stack>
          )}
        </Box>
        <HStack
          justifyContent="space-between"
          flexDirection={hasPrevious ? 'row' : 'row-reverse'}
          mt="12"
          mb="2"
        >
          {hasPrevious && (
            <Button
              leftIcon={
                <ChevronLeftIcon
                  color={theme.colors.royalBlue[500]}
                  width="20px"
                  height="20px"
                />
              }
              variant="quiet"
              onClick={onPrevious}
            >
              <Text
                fontSize="md"
                color="royalBlue.500"
              >
                Previous
              </Text>
            </Button>
          )}
          <Button
            isDisabled={!isFormValid || !(dateField?.length > 1)}
            isLoading={isSubmitting}
            type="submit"
          >
            {!screening ? 'Add' : 'Save'}
          </Button>
        </HStack>
      </form>
    </FormProvider>
  );
}
