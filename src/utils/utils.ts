import dayjs from 'dayjs';

import { API_GATEWAY_URL } from '@lib/constants';
import { Patient } from 'src/gql/graphql';

export const getAge = (dateString: Patient['birthDate']) => {
  const today = new Date();
  const birthDate = new Date(dateString || '');
  let age = today.getFullYear() - birthDate.getFullYear();
  const m = today.getMonth() - birthDate.getMonth();
  if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }
  return age;
};

export const shuffleArray = <T>(array: T[]): T[] => {
  return array
    .map((item) => ({ item, sort: Math.random() }))
    .sort((a, b) => a.sort - b.sort)
    .map(({ item }) => item);
};

export const getAssetUrl = (path: string) => {
  return `${API_GATEWAY_URL}/${path}`;
};

function levenshteinDistance(s1: string, s2: string): number {
  const len1 = s1.length;
  const len2 = s2.length;

  const dp: number[][] = Array.from(Array(len1 + 1), () => Array(len2 + 1).fill(0));

  for (let i = 0; i <= len1; i++) {
    dp[i][0] = i;
  }
  for (let j = 0; j <= len2; j++) {
    dp[0][j] = j;
  }

  for (let i = 1; i <= len1; i++) {
    for (let j = 1; j <= len2; j++) {
      const cost = s1[i - 1] === s2[j - 1] ? 0 : 1;
      dp[i][j] = Math.min(dp[i - 1][j] + 1, dp[i][j - 1] + 1, dp[i - 1][j - 1] + cost);
    }
  }

  return dp[len1][len2];
}

export function findMostMatchingString(strings: string[], target: string): string | null {
  if (strings.length === 0) {
    return null;
  }

  let mostSimilarString = strings[0];
  let minDistance = levenshteinDistance(strings[0], target);

  for (let i = 1; i < strings.length; i++) {
    const currentDistance = levenshteinDistance(strings[i], target);
    if (currentDistance < minDistance) {
      minDistance = currentDistance;
      mostSimilarString = strings[i];
    }
  }

  return mostSimilarString;
}

// const result = findMostMatchingString(stringList, targetString);
export const sanitizePhoneNumber = (phone: string | undefined) => {
  const digits = phone?.replace(/\D/g, ''); // Remove non-numeric characters
  return digits?.length === 12 && digits?.startsWith('91') ? digits?.slice(2) : digits?.length === 10 ? digits : null;
};

export function safeJsonParse(str: string): any {
  try {
    return JSON.parse(str);
  } catch (e) {
    console.warn('JSON parse failed, using raw string:', str);
    return str;
  }
}
export function getDeeplinkPath(response: any): string | undefined {
  if (response?.data_parsed?.$deeplink_path) {
    return response.data_parsed.$deeplink_path;
  }
  if (typeof response?.data === 'string') {
    try {
      const parsedData = JSON.parse(response?.data);
      return parsedData?.$deeplink_path;
    } catch (e) {
      console.warn('Nested data parse failed:', response.data, e);
    }
  }
  if (response?.data?.$deeplink_path) {
    return response.data.$deeplink_path;
  }
  return response?.$deeplink_path;
}

/**
 * Formats a date value for display.
 * If the value is 'Present' or 'Unknown', returns it as-is.
 * Otherwise, formats the date as 'DD MMM YYYY' (e.g., '16 Jun 2025').
 * @param {string | Date | null | undefined} date - The date value to format
 * @returns {string} The formatted date string, 'Present', 'Unknown', or empty string
 */
export const formatDateForDisplay = (date: string | Date | null | undefined): string => {
  if (!date) return '';

  // Handle special string values
  if (typeof date === 'string') {
    if (date === 'Present' || date === 'Unknown') {
      return date;
    }
  }

  // Format the date using dayjs
  const parsedDate = dayjs(date);
  if (!parsedDate.isValid()) {
    return 'Unknown';
  }

  return parsedDate.format('DD MMM YYYY');
};
