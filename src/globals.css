@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Material+Symbols+Rounded:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200');

@tailwind base;

@layer base {
  .material-symbols-rounded {
    font-variation-settings: 'FILL' 0, 'wght' 400, 'GRAD' 200, 'opsz' 40;
    font-size: 1.25rem;
  }

  :focus-visible {
    outline: initial;
  }
}

@tailwind components;
@tailwind utilities;

/* FONTS */

@font-face {
  font-family: 'Apercu';
  font-style: normal;
  font-weight: 100;
  src: url('assets/fonts/apercu/apercu-thin-pro.woff2') format('woff2'),
    url('assets/fonts/apercu/apercu-thin-pro.woff') format('woff');
}

@font-face {
  font-family: 'Apercu';
  font-style: normal;
  font-weight: 200;
  src: url('assets/fonts/apercu/apercu-extralight-pro.woff2') format('woff2'),
    url('assets/fonts/apercu/apercu-extralight-pro.woff') format('woff');
}

@font-face {
  font-family: 'Apercu';
  font-style: normal;
  font-weight: 300;
  src: url('assets/fonts/apercu/apercu-light-pro.woff2') format('woff2'),
    url('assets/fonts/apercu/apercu-light-pro.woff') format('woff');
}

@font-face {
  font-family: 'Apercu';
  font-style: normal;
  font-weight: 400;
  src: url('assets/fonts/apercu/apercu-regular-pro.woff2') format('woff2'),
    url('assets/fonts/apercu/apercu-regular-pro.woff') format('woff');
}

@font-face {
  font-family: 'Apercu';
  font-style: normal;
  font-weight: 500;
  src: url('assets/fonts/apercu/apercu-medium-pro.woff2') format('woff2'),
    url('assets/fonts/apercu/apercu-medium-pro.woff') format('woff');
}

@font-face {
  font-family: 'Apercu';
  font-style: normal;
  font-weight: 700;
  src: url('assets/fonts/apercu/apercu-bold-pro.woff2') format('woff2'),
    url('assets/fonts/apercu/apercu-bold-pro.woff') format('woff');
}

@font-face {
  font-family: 'Apercu';
  font-style: normal;
  font-weight: 800;
  src: url('assets/fonts/apercu/apercu-extrabold-pro.woff2') format('woff2'),
    url('assets/fonts/apercu/apercu-extrabold-pro.woff') format('woff');
}

@font-face {
  font-family: 'Apercu';
  font-style: normal;
  font-weight: 900;
  src: url('assets/fonts/apercu/apercu-black-pro.woff2') format('woff2'),
    url('assets/fonts/apercu/apercu-black-pro.woff') format('woff');
}

@font-face {
  font-family: 'Apercu Mono';
  font-style: normal;
  font-weight: 100;
  src: url('assets/fonts/apercu-mono/apercu-mono-pro-light.ttf') format('truetype');
}

@font-face {
  font-family: 'Apercu Mono';
  font-style: normal;
  font-weight: 400;
  src: url('assets/fonts/apercu-mono/apercu-mono-pro-regular.ttf') format('truetype');
}

@font-face {
  font-family: 'Apercu Mono';
  font-style: normal;
  font-weight: 500;
  src: url('assets/fonts/apercu-mono/apercu-mono-pro-medium.ttf') format('truetype');
}

@font-face {
  font-family: 'Apercu Mono';
  font-style: normal;
  font-weight: 600;
  src: url('assets/fonts/apercu-mono/apercu-mono-pro-bold.ttf') format('truetype');
}

@font-face {
  font-family: 'P22Mackinac';
  font-style: normal;
  font-weight: 400;
  src: url('assets/fonts/p22mackinac/P22Mackinac-Book-webfont.woff2') format('woff2'),
    url('assets/fonts/p22mackinac/P22Mackinac-Book-webfont.woff') format('woff');
}

@font-face {
  font-family: 'P22Mackinac';
  font-style: normal;
  font-weight: 500;
  src: url('assets/fonts/p22mackinac/P22Mackinac-Medium-webfont.woff2') format('woff2'),
    url('assets/fonts/p22mackinac/P22Mackinac-Medium-webfont.woff') format('woff');
}

@font-face {
  font-family: 'P22Mackinac';
  font-style: normal;
  font-weight: 700;
  src: url('assets/fonts/p22mackinac/P22Mackinac-Bold-webfont.woff2') format('woff2'),
    url('assets/fonts/p22mackinac/P22Mackinac-Bold-webfont.woff') format('woff');
}

@font-face {
  font-family: 'P22Mackinac';
  font-style: normal;
  font-weight: 800;
  src: url('assets/fonts/p22mackinac/P22Mackinac-ExtraBold-webfont.woff2') format('woff2'),
    url('assets/fonts/p22mackinac/P22Mackinac-ExtraBold-webfont.woff') format('woff');
}

/* Chrome autofill. TBD a better way. */
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  transition-delay: 86400s;
}

#root {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.apercu-font,
.apercu-font * {
  font-family: 'Apercu';
}

/* width */
::-webkit-scrollbar {
  width: 10px;
}
@media screen and (max-width: 450px) {
  ::-webkit-scrollbar {
    width: 6px;
  }
}
/* Track */
::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 5px;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: #a9aeaf;
  border-radius: 5px;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: #888d8f;
  border-radius: 5px;
}
.capitalize {
  text-transform: capitalize;
}

.public-select-input {
  color: var(--chakra-colors-iris-500) !important;
}
[class^='public-select'] {
  opacity: 1 !important;
  color: var(--chakra-colors-iris-500) !important;
}
[class^='public-select__indicators'] {
  display: none !important;
}