// Package modules
import React from 'react';
// import { NavLink } from 'react-router-dom';
import { Box, Flex, Text } from '@chakra-ui/react';
// Local modules
import CircDecorationLayout from '@components/CircDecorationLayout';
import { ROUTE_ACTIONS, ROUTE_VARIABLES } from '@user/lib/constants';

import AppError from './AppError';
import ShareErrorPage from 'src/app/user/share-profile/ShareError';

export function ErrorContent(error: any = {}) {
  const { DASHBOARD } = ROUTE_VARIABLES;
  const { VIEW } = ROUTE_ACTIONS;
  const isSharePage =
    window.location.pathname.includes('share-records') || window.location.pathname.includes('share-profile');
  const isSessionExpired =
    error?.error?.status === 401 ||
    (error?.status === 401 && error?.message?.includes('Session expired')) ||
    (error?.error?.status === 401 && error?.message?.includes('Session expired')) ||
    (error?.error?.error?.status === 401 && error?.message?.includes('Session expired'));

  return isSharePage && (isSessionExpired || error?.error?.sourceError?.response?.status || error.error?.code) ? (
    <CircDecorationLayout>
      <Flex
        alignItems="center"
        height="calc(100vh - 128px)"
        zIndex={1}
      >
        <ShareErrorPage error={error} />
      </Flex>
    </CircDecorationLayout>
  ) : (
    <Box pt="42px">
      <Box className="mx-auto max-w-lg rounded bg-white px-4 py-6">
        <Text className="mb-4 text-lg font-bold">Sorry, an unexpected error has occurred.</Text>
        <AppError error={error} />
        <a
          className="mt-3 inline-block text-sm text-blue-500 underline underline-offset-2"
          href={`/${DASHBOARD}/${VIEW}`}
        >
          Navigate home
        </a>
      </Box>
    </Box>
  );
}

export class ErrorBoundary extends React.Component {
  constructor(props: any) {
    super(props);

    this.state = { error: null };
  }

  static getDerivedStateFromError(error: any) {
    return { error };
  }

  componentDidCatch(error: any, errorInfo: any) {
    console.warn('ErrorBoundary caught an error:', error, errorInfo);
  }

  render() {
    const { error }: any = this.state;

    if (error) {
      // Render fallback if defined.
      const { fallback }: any = this.props;
      if (fallback) {
        return fallback;
      }

      // Render generic error page.
      return <ErrorContent error={error} />;
    }

    const { children }: any = this.props;
    return children;
  }
}
