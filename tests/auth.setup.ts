import { test, expect } from '@playwright/test';

/**
 * Parametrized auth user credentials,
 * we split this out as it will be easier to arrange multi user tests
 */
export type AuthOptions = {
  auth: { phoneNumber: string; pin: string };
};

export const testAuth = test.extend<AuthOptions>({
  auth: [{ phoneNumber: '1234567890', pin: '111111' }, { option: true }],
});

testAuth('auth setup', async ({ page, auth }) => {
  await page.goto('/');

  await expect(page.getByRole('heading', { name: 'Log into your Fluent' })).toBeVisible();
  await page.getByLabel('Phone Number').fill(auth.phoneNumber);

  await page.getByRole('button', { name: 'Continue' }).click();

  for (const [index, pin] of auth.pin.split('').entries()) {
    await page.getByLabel('Please enter your pin code').nth(index).fill(pin);
  }

  await page.getByRole('button', { name: 'Verify' }).click();
  await expect(page.getByRole('heading', { name: /Hello/ })).toBeVisible();

  await page.context().storageState({ path: 'playwright/.auth/session.json' });
});
