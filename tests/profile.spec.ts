import { test, expect } from '@playwright/test';

test('Basic Info', async ({ page }) => {
  await page.goto('/');
  await expect(page.getByRole('heading', { name: /Hello/ })).toBeVisible();

  await page.getByRole('link', { name: 'avatar' }).first().click();

  await expect(page.getByText('Basic Info')).toBeVisible();

  await expect(page.locator('role=group', { hasText: 'Height' })).toContainText('178 cm');

  await expect(page.locator('role=group', { hasText: 'Weight' })).toContainText('78 kg');
});
